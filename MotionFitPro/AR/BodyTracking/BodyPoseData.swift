//
//  BodyPoseData.swift
//  MotionFitPro
//
//  Created by Cascade on 2025-07-14.
//

import Foundation
import RealityKit
import ARKit
import simd

/// A structure to hold processed 3D body pose data for a single frame.
struct BodyPoseData: Sendable, Codable {
    let timestamp: TimeInterval
    let worldTransform: simd_float4x4
    let joints: [String: Joint]
    let confidence: Float
    let frameID: UUID
    
    init(timestamp: TimeInterval = Date().timeIntervalSince1970, 
         worldTransform: simd_float4x4 = matrix_identity_float4x4,
         joints: [String: Joint], 
         confidence: Float,
         frameID: UUID = UUID()) {
        self.timestamp = timestamp
        self.worldTransform = worldTransform
        self.joints = joints
        self.confidence = confidence
        self.frameID = frameID
    }
}

/// Represents a single joint in the body skeleton.
struct Joint: Sendable, Codable {
    let position: simd_float3
    let confidence: Float
    let timestamp: Date
    
    init(position: simd_float3, confidence: Float, timestamp: Date = Date()) {
        self.position = position
        self.confidence = confidence
        self.timestamp = timestamp
    }
}

// MARK: - Exercise Type Support
enum ExerciseType: String, CaseIterable, Sendable, Codable {
    case squat = "squat"
    case pushUp = "pushUp"
    case plank = "plank"
    case burpee = "burpee"
    case mountainClimber = "mountainClimber"
    
    var displayName: String {
        switch self {
        case .squat: return "Squat"
        case .pushUp: return "Push-Up"
        case .plank: return "Plank"
        case .burpee: return "Burpee"
        case .mountainClimber: return "Mountain Climber"
        }
    }
}

// MARK: - ARKit Integration
extension BodyPoseData {
    /// Initialize from ARBodyAnchor for real ARKit integration
    init?(from bodyAnchor: ARBodyAnchor) {
        self.timestamp = bodyAnchor.timestamp
        self.worldTransform = bodyAnchor.transform

        var jointData: [String: Joint] = [:]
        let skeleton = bodyAnchor.skeleton
        let jointNames = ARSkeletonDefinition.defaultBody3D.jointNames

        for jointName in jointNames {
            if let aname = ARSkeleton.JointName(rawValue: jointName) {
                let modelTransform = skeleton.modelTransform(for: aname) ?? matrix_identity_float4x4
                let position = simd_make_float3(modelTransform.columns.3)
                
                // Use body tracking confidence
                let jointConfidence: Float = bodyAnchor.isTracked ? 0.9 : 0.3
                jointData[jointName] = Joint(position: position, confidence: jointConfidence)
            }
        }

        self.joints = jointData
        self.confidence = bodyAnchor.estimatedScaleFactor > 0.5 ? 0.9 : 0.5
        self.frameID = UUID()
    }
    
    /// Create mock data for testing purposes
    static func mockStandingPose() -> BodyPoseData {
        let baseHeight: Float = 1.7
        
        var joints: [String: Joint] = [:]
        
        // Core joints with ARKit standard names
        joints["root"] = Joint(position: simd_float3(0, baseHeight * 0.6, 0), confidence: 0.95)
        joints["spine_7"] = Joint(position: simd_float3(0, baseHeight * 0.8, 0), confidence: 0.95)
        joints["head"] = Joint(position: simd_float3(0, baseHeight, 0), confidence: 0.95)
        
        // Arms
        joints["left_arm"] = Joint(position: simd_float3(-0.15, baseHeight * 0.75, 0), confidence: 0.9)
        joints["right_arm"] = Joint(position: simd_float3(0.15, baseHeight * 0.75, 0), confidence: 0.9)
        joints["left_hand"] = Joint(position: simd_float3(-0.2, baseHeight * 0.5, 0), confidence: 0.85)
        joints["right_hand"] = Joint(position: simd_float3(0.2, baseHeight * 0.5, 0), confidence: 0.85)
        
        // Legs
        joints["left_leg"] = Joint(position: simd_float3(-0.1, baseHeight * 0.3, 0), confidence: 0.9)
        joints["right_leg"] = Joint(position: simd_float3(0.1, baseHeight * 0.3, 0), confidence: 0.9)
        joints["left_foot"] = Joint(position: simd_float3(-0.1, 0, 0), confidence: 0.85)
        joints["right_foot"] = Joint(position: simd_float3(0.1, 0, 0), confidence: 0.85)
        
        return BodyPoseData(joints: joints, confidence: 0.9)
    }
    
    /// Get joint by name with safe access
    func joint(named name: String) -> Joint? {
        return joints[name]
    }
    
    /// Check if pose has minimum required joints for exercise analysis
    func hasRequiredJoints(for exerciseType: ExerciseType) -> Bool {
        let requiredJoints = getRequiredJoints(for: exerciseType)
        return requiredJoints.allSatisfy { jointName in
            guard let joint = joints[jointName] else { return false }
            return joint.confidence > 0.5
        }
    }
    
    private func getRequiredJoints(for exerciseType: ExerciseType) -> [String] {
        switch exerciseType {
        case .squat:
            return ["root", "left_leg", "right_leg", "left_foot", "right_foot"]
        case .pushUp:
            return ["head", "spine_7", "root", "left_arm", "right_arm", "left_hand", "right_hand"]
        case .plank:
            return ["head", "spine_7", "root", "left_arm", "right_arm", "left_foot", "right_foot"]
        case .burpee:
            return ["root", "left_leg", "right_leg", "left_arm", "right_arm", "head"]
        case .mountainClimber:
            return ["head", "spine_7", "root", "left_leg", "right_leg", "left_hand", "right_hand"]
        }
    }
}

// MARK: - Math Helpers
extension BodyPoseData {
    /// Calculate distance between two joints
    func distance(from joint1: String, to joint2: String) -> Float? {
        guard let j1 = joints[joint1], let j2 = joints[joint2] else { return nil }
        return simd_length(j1.position - j2.position)
    }
    
    /// Calculate angle between three joints (center joint is the vertex)
    func angle(joint1: String, center: String, joint2: String) -> Float? {
        guard let j1 = joints[joint1], 
              let jc = joints[center], 
              let j2 = joints[joint2] else { return nil }
        
        let vector1 = j1.position - jc.position
        let vector2 = j2.position - jc.position
        
        let dotProduct = simd_dot(vector1, vector2)
        let magnitude1 = simd_length(vector1)
        let magnitude2 = simd_length(vector2)
        
        guard magnitude1 > 0 && magnitude2 > 0 else { return nil }
        
        let cosAngle = dotProduct / (magnitude1 * magnitude2)
        let clampedCos = max(-1, min(1, cosAngle))
        
        return acos(clampedCos) * 180.0 / Float.pi // Return in degrees
    }
}
