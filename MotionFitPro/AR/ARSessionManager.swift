//
//  ARSessionManager.swift
//  MotionFitPro
//
//  Created by Cascade on 2025-07-14.
//

import Foundation
import ARKit
import Combine
import os.log

/// Manages the ARKit session for body tracking, processes frame data, and monitors performance.
@MainActor
class ARSessionManager: NSObject, ObservableObject {

    // MARK: - Published Properties

    @Published var sessionState: ARSessionState = .stopped
    @Published var trackingQuality: TrackingQuality = .initializing
    @Published var latestPose: BodyPoseData?
    @Published var performanceMetrics = PerformanceMetrics()

    // MARK: - Public Properties

    static let shared = ARSessionManager()

    // MARK: - Private Properties

    private let session = ARSession()
    private let frameProcessor = CameraFrameProcessor()
    private var cancellables = Set<AnyCancellable>()
    private let logger = Logger(subsystem: "com.yourcompany.MotionFitPro", category: "ARSessionManager")

    private var frameCounter = 0
    private var lastFrameTimestamp: TimeInterval = 0

    // MARK: - Initialization

    private override init() {
        super.init()
        session.delegate = self
        subscribeToPublishers()
    }

    private func subscribeToPublishers() {
        frameProcessor.bodyPosePublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] poseData in
                self?.latestPose = poseData
            }
            .store(in: &cancellables)
    }

    // MARK: - Session Management

    /// Configures and starts the AR body tracking session.
    func startSession() {
        guard ARBodyTrackingConfiguration.isSupported else {
            logger.error("AR Body Tracking is not supported on this device.")
            sessionState = .failed(ARError(.unsupportedConfiguration))
            return
        }

        let configuration = ARBodyTrackingConfiguration()

        // Enable high-frame-rate capturing on supported devices (iPhone 13 Pro and later)
        if let highestFrameRate = ARBodyTrackingConfiguration.supportedVideoFormats.first(where: { $0.framesPerSecond >= 60 }) {
            configuration.videoFormat = highestFrameRate
            logger.log("Configured for 60 FPS video format.")
        } else {
            logger.log("60 FPS not supported, using default frame rate.")
        }

        configuration.automaticSkeletonScaleEstimationEnabled = true
        configuration.isAutoFocusEnabled = true

        session.run(configuration, options: [.resetTracking, .removeExistingAnchors])
        sessionState = .running
        logger.log("AR session started.")
    }

    /// Pauses the AR session.
    func stopSession() {
        session.pause()
        sessionState = .stopped
        logger.log("AR session stopped.")
    }

    // MARK: - Device Capability Check

    static var isSupported: Bool {
        return ARBodyTrackingConfiguration.isSupported
    }
}

// MARK: - ARSessionDelegate
extension ARSessionManager: ARSessionDelegate {
    func session(_ session: ARSession, didUpdate frame: ARFrame) {
        frameProcessor.session(session, didUpdate: frame)
        updatePerformanceMetrics(frame: frame)
        updateTrackingQuality(for: frame.camera)
    }

    func session(_ session: ARSession, didFailWithError error: Error) {
        logger.error("ARSession failed with error: \(error.localizedDescription)")
        DispatchQueue.main.async {
            self.sessionState = .failed(error)
        }
    }

    func sessionWasInterrupted(_ session: ARSession) {
        logger.warning("ARSession was interrupted.")
        DispatchQueue.main.async {
            self.sessionState = .interrupted
            self.trackingQuality = .poor("Session interrupted")
        }
    }

    func sessionInterruptionEnded(_ session: ARSession) {
        logger.info("ARSession interruption ended. Attempting to restart.")
        startSession()
    }
}

// MARK: - Performance & Quality Monitoring
extension ARSessionManager {
    private func updatePerformanceMetrics(frame: ARFrame) {
        frameCounter += 1
        let currentTime = frame.timestamp
        let deltaTime = currentTime - lastFrameTimestamp

        if deltaTime >= 1.0 { // Update FPS every second
            let fps = Double(frameCounter) / deltaTime
            DispatchQueue.main.async {
                self.performanceMetrics.fps = fps
            }
            frameCounter = 0
            lastFrameTimestamp = currentTime
            logger.debug("FPS: \(String(format: "%.2f", fps))")
        }
    }

    private func updateTrackingQuality(for camera: ARCamera) {
        let newQuality: TrackingQuality
        switch camera.trackingState {
        case .normal:
            newQuality = .good
        case .notAvailable:
            newQuality = .poor("Tracking not available.")
        case .limited(let reason):
            let reasonText: String
            switch reason {
            case .excessiveMotion:
                reasonText = "Excessive motion."
            case .insufficientFeatures:
                reasonText = "Insufficient features."
            case .initializing:
                newQuality = .initializing
                return
            case .relocalizing:
                newQuality = .poor("Relocalizing...")
                return
            @unknown default:
                reasonText = "Unknown reason."
            }
            newQuality = .limited(reasonText)
            logger.warning("Tracking quality is limited: \(reasonText)")
        }
        
        if newQuality != self.trackingQuality {
            DispatchQueue.main.async {
                self.trackingQuality = newQuality
            }
        }
    }
}

// MARK: - Supporting Types

extension ARSessionManager {
    enum ARSessionState: Equatable {
        case running
        case stopped
        case failed(Error)
        case interrupted

        static func == (lhs: ARSessionManager.ARSessionState, rhs: ARSessionManager.ARSessionState) -> Bool {
            switch (lhs, rhs) {
            case (.running, .running), (.stopped, .stopped), (.interrupted, .interrupted):
                return true
            case (.failed(let lError), .failed(let rError)):
                return (lError as NSError).code == (rError as NSError).code
            default:
                return false
            }
        }
    }

    enum TrackingQuality: Equatable {
        case initializing
        case good
        case limited(String)
        case poor(String)
    }

    struct PerformanceMetrics {
        var fps: Double = 0.0
    }
}