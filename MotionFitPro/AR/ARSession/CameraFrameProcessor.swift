//
//  CameraFrameProcessor.swift
//  MotionFitPro
//
//  Created by Cascade on 2025-07-14.
//

import Foundation
import ARKit
import Combine

/// Processes ARFrames to extract and publish body pose data.
class CameraFrameProcessor: NSObject, ARSessionDelegate {
    
    /// A publisher that emits the latest processed body pose data.
    let bodyPosePublisher = PassthroughSubject<BodyPoseData, Never>()
    
    /// A circular buffer to hold recent pose data for analysis (e.g., smoothing).
    private var poseBuffer = [BodyPoseData]()
    private let bufferSize = 10 // Store the last 10 poses

    // MARK: - ARSessionDelegate

    func session(_ session: ARSession, didUpdate frame: ARFrame) {
        // Process the frame to find body anchors
        guard let bodyAnchor = frame.anchors.first(where: { $0 is ARBodyAnchor }) as? ARBodyAnchor else {
            // No body anchor found in this frame
            return
        }

        // Convert the ARBodyAnchor to our custom BodyPoseData structure
        guard let poseData = BodyPoseData(from: bodyAnchor) else {
            // Failed to create BodyPoseData (e.g., invalid data)
            return
        }

        // Add to buffer and publish
        addToBuffer(poseData)
        bodyPosePublisher.send(poseData)
    }

    func session(_ session: ARSession, didFailWithError error: Error) {
        // Handle session errors, e.g., by logging them.
        print("ARSession failed with error: \(error.localizedDescription)")
    }

    func sessionWasInterrupted(_ session: ARSession) {
        // Handle session interruptions (e.g., user switches to another app).
        print("ARSession was interrupted.")
    }

    func sessionInterruptionEnded(_ session: ARSession) {
        // Handle session interruption ending.
        print("ARSession interruption ended.")
    }

    // MARK: - Buffer Management

    /// Adds a new pose to the circular buffer.
    private func addToBuffer(_ pose: BodyPoseData) {
        if poseBuffer.count >= bufferSize {
            poseBuffer.removeFirst()
        }
        poseBuffer.append(pose)
    }
    
    /// Provides access to the current buffer of poses.
    func getPoseBuffer() -> [BodyPoseData] {
        return poseBuffer
    }
}
