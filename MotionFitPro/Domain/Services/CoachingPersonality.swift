import Foundation

/// Defines different coaching personalities and their characteristics
enum CoachingPersonality: String, CaseIterable, Codable {
    case supportive = "Supportive"
    case technical = "Technical"
    case motivational = "Motivational"
    case safetyFirst = "Safety-First"
    
    var displayName: String {
        return rawValue
    }
    
    var description: String {
        switch self {
        case .supportive:
            return "Encouraging and patient guidance"
        case .technical:
            return "Detailed biomechanics focus"
        case .motivational:
            return "High-energy and challenging"
        case .safetyFirst:
            return "Conservative with injury prevention emphasis"
        }
    }
    
    var icon: String {
        switch self {
        case .supportive:
            return "heart.fill"
        case .technical:
            return "brain.head.profile"
        case .motivational:
            return "bolt.fill"
        case .safetyFirst:
            return "shield.fill"
        }
    }
    
    var color: String {
        switch self {
        case .supportive:
            return "pink"
        case .technical:
            return "blue"
        case .motivational:
            return "orange"
        case .safetyFirst:
            return "green"
        }
    }
}

/// Manages personality-specific coaching behavior
struct CoachingPersonalityManager {
    
    // MARK: - Encouragement Messages
    
    static func getEncouragementMessage(
        personality: CoachingPersonality,
        context: EncouragementContext,
        skillLevel: SkillLevel
    ) -> String {
        switch personality {
        case .supportive:
            return getSupportiveEncouragement(context: context, skillLevel: skillLevel)
        case .technical:
            return getTechnicalEncouragement(context: context, skillLevel: skillLevel)
        case .motivational:
            return getMotivationalEncouragement(context: context, skillLevel: skillLevel)
        case .safetyFirst:
            return getSafetyFirstEncouragement(context: context, skillLevel: skillLevel)
        }
    }
    
    private static func getSupportiveEncouragement(context: EncouragementContext, skillLevel: SkillLevel) -> String {
        switch context {
        case .goodForm:
            return [
                "Beautiful form! You're doing amazing!",
                "That's perfect! Keep up the wonderful work!",
                "Excellent technique! You should be proud!",
                "Great job! Your form is looking fantastic!"
            ].randomElement() ?? "Great form!"
            
        case .repCompletion:
            return [
                "Nice work on that rep! You're getting stronger!",
                "That was beautiful! One step closer to your goal!",
                "Wonderful! Each rep is making you better!",
                "Great rep! You're doing so well!"
            ].randomElement() ?? "Great rep!"
            
        case .setCompletion:
            return [
                "Amazing set! You should feel proud of that effort!",
                "Fantastic work! Take a moment to appreciate what you just accomplished!",
                "That was incredible! You're making real progress!",
                "Beautiful set! Your dedication is really showing!"
            ].randomElement() ?? "Amazing set!"
            
        case .improvement:
            return [
                "I can see you getting better! Your hard work is paying off!",
                "What an improvement! You're becoming stronger every day!",
                "Your progress is wonderful to see! Keep believing in yourself!",
                "That improvement is amazing! You're doing something special here!"
            ].randomElement() ?? "Great improvement!"
            
        case .struggleSupport:
            return [
                "It's okay, everyone has challenging moments. You're still doing great!",
                "Take your time. Progress isn't always linear, and that's perfectly normal!",
                "You're working hard, and that's what matters most!",
                "This is tough, but so are you! Take a breath and keep going!"
            ].randomElement() ?? "You're doing great!"
        }
    }
    
    private static func getTechnicalEncouragement(context: EncouragementContext, skillLevel: SkillLevel) -> String {
        switch context {
        case .goodForm:
            return [
                "Excellent biomechanics! Your joint alignment is optimal!",
                "Perfect movement pattern! Your kinetic chain is working efficiently!",
                "Outstanding technique! Your force production is maximized!",
                "Precise execution! Your neuromuscular coordination is excellent!"
            ].randomElement() ?? "Perfect technique!"
            
        case .repCompletion:
            return [
                "Solid rep! Your movement velocity and range of motion were on point!",
                "Good execution! Your muscle activation pattern was ideal!",
                "Nice rep! Your eccentric and concentric phases were well controlled!",
                "Quality movement! Your stabilizer muscles engaged properly!"
            ].randomElement() ?? "Good rep!"
            
        case .setCompletion:
            return [
                "Excellent set! You maintained proper form throughout the entire range!",
                "Great set! Your movement consistency shows good motor learning!",
                "Strong set! Your fatigue management was technically sound!",
                "Quality set! Your tempo and technique remained optimal!"
            ].randomElement() ?? "Excellent set!"
            
        case .improvement:
            return [
                "Your movement efficiency has improved significantly!",
                "I'm seeing better motor unit recruitment patterns!",
                "Your proprioceptive awareness has enhanced noticeably!",
                "Excellent progression in your movement mechanics!"
            ].randomElement() ?? "Great improvement!"
            
        case .struggleSupport:
            return [
                "Focus on maintaining your movement pattern even under fatigue!",
                "Reduce the range of motion if needed to maintain proper form!",
                "Engage your core stabilizers to support the primary movement!",
                "Concentrate on the eccentric phase for better muscle activation!"
            ].randomElement() ?? "Focus on form!"
        }
    }
    
    private static func getMotivationalEncouragement(context: EncouragementContext, skillLevel: SkillLevel) -> String {
        switch context {
        case .goodForm:
            return [
                "YES! That's what I'm talking about! Crushing it!",
                "FIRE! You're a machine right now!",
                "Beast mode activated! That form is deadly!",
                "Unstoppable! You're owning this workout!"
            ].randomElement() ?? "YES! Crushing it!"
            
        case .repCompletion:
            return [
                "BOOM! Another one down! You're on fire!",
                "That's how it's done! Keep that energy up!",
                "Powerful! You're building something special here!",
                "Savage! Nothing can stop you today!"
            ].randomElement() ?? "BOOM! Great rep!"
            
        case .setCompletion:
            return [
                "CRUSHING IT! That set was absolutely phenomenal!",
                "DOMINATING! You just leveled up right there!",
                "UNSTOPPABLE! You're in the zone now!",
                "LEGENDARY! That's championship-level effort!"
            ].randomElement() ?? "CRUSHING IT!"
            
        case .improvement:
            return [
                "You're evolving into a fitness BEAST!",
                "That improvement is INSANE! You're unlocking your potential!",
                "LEVEL UP! You're becoming unstoppable!",
                "BREAKTHROUGH! This is your moment to shine!"
            ].randomElement() ?? "LEVEL UP!"
            
        case .struggleSupport:
            return [
                "This is where champions are made! Push through!",
                "The burn means it's working! You've got this!",
                "Pain is temporary, but quitting lasts forever! Keep going!",
                "This is your moment! Show me what you're made of!"
            ].randomElement() ?? "You've got this!"
        }
    }
    
    private static func getSafetyFirstEncouragement(context: EncouragementContext, skillLevel: SkillLevel) -> String {
        switch context {
        case .goodForm:
            return [
                "Perfect! Safe and effective form keeps you training consistently!",
                "Excellent! You're protecting your joints while building strength!",
                "Great technique! Injury prevention is the key to long-term success!",
                "Smart movement! Quality over quantity always wins!"
            ].randomElement() ?? "Perfect form!"
            
        case .repCompletion:
            return [
                "Good rep! Remember, controlled movement prevents injury!",
                "Nice work! You're building strength the smart way!",
                "Solid rep! Consistency and safety lead to the best results!",
                "Well done! Your future self will thank you for this careful approach!"
            ].randomElement() ?? "Good rep!"
            
        case .setCompletion:
            return [
                "Smart set! You prioritized safety and still got great work done!",
                "Excellent! You listened to your body and maintained good form!",
                "Great set! This is how you build sustainable fitness!",
                "Perfect! You're training smart for long-term success!"
            ].randomElement() ?? "Smart set!"
            
        case .improvement:
            return [
                "Wonderful improvement! You're getting stronger safely!",
                "Great progress! Your careful approach is paying off!",
                "Excellent! Steady, safe progress is the best kind!",
                "Smart improvement! You're building a foundation that will last!"
            ].randomElement() ?? "Great progress!"
            
        case .struggleSupport:
            return [
                "It's okay to slow down. Better safe than sorry!",
                "Listen to your body. Rest when you need it!",
                "Take a break if needed. Consistency matters more than intensity!",
                "Remember, there's no shame in stopping if something doesn't feel right!"
            ].randomElement() ?? "Listen to your body!"
        }
    }
    
    // MARK: - Form Correction Messages
    
    static func getFormCorrectionMessage(
        personality: CoachingPersonality,
        issue: FormIssue,
        skillLevel: SkillLevel
    ) -> String {
        let baseCorrection = issue.correction
        
        switch personality {
        case .supportive:
            return "Hey, let's adjust something small. \(baseCorrection) You're doing great overall!"
            
        case .technical:
            return "Form optimization needed: \(baseCorrection) This will improve your movement efficiency."
            
        case .motivational:
            return "Let's perfect this! \(baseCorrection) You're going to crush it with better form!"
            
        case .safetyFirst:
            return "For your safety, please \(baseCorrection.lowercased()) This helps prevent injury."
        }
    }
    
    // MARK: - Rest Guidance Messages
    
    static func getRestGuidanceMessage(
        personality: CoachingPersonality,
        duration: TimeInterval,
        context: RestContext
    ) -> String {
        let durationText = Int(duration)
        
        switch personality {
        case .supportive:
            switch context {
            case .betweenSets:
                return "Take \(durationText) seconds to rest. You've earned it! Breathe deeply and prepare for your next amazing set."
            case .betweenExercises:
                return "Great work! Rest for \(durationText) seconds. Take this time to appreciate what you just accomplished."
            case .recovery:
                return "Your body needs this rest. Take \(durationText) seconds to recover properly. You're being smart about your training."
            }
            
        case .technical:
            switch context {
            case .betweenSets:
                return "Rest period: \(durationText) seconds. Allow your ATP-PC system to recover for optimal performance."
            case .betweenExercises:
                return "\(durationText) second transition period. This allows proper metabolic recovery between movement patterns."
            case .recovery:
                return "Extended recovery: \(durationText) seconds. This prevents accumulated fatigue and maintains movement quality."
            }
            
        case .motivational:
            switch context {
            case .betweenSets:
                return "Active rest for \(durationText) seconds! Stay focused! Your next set is going to be EPIC!"
            case .betweenExercises:
                return "\(durationText) seconds to recharge! You're building something incredible here!"
            case .recovery:
                return "Recovery time: \(durationText) seconds. Use this time to visualize dominating your next exercise!"
            }
            
        case .safetyFirst:
            switch context {
            case .betweenSets:
                return "Important rest period: \(durationText) seconds. This prevents overexertion and maintains safe movement patterns."
            case .betweenExercises:
                return "Transition rest: \(durationText) seconds. Proper rest reduces injury risk and maintains exercise quality."
            case .recovery:
                return "Recovery break: \(durationText) seconds. Listen to your body and rest as long as needed."
            }
        }
    }
    
    // MARK: - Safety Warning Messages
    
    static func getSafetyWarningMessage(
        personality: CoachingPersonality,
        warning: SafetyWarning
    ) -> String {
        switch personality {
        case .supportive:
            return "I'm concerned about your safety. \(warning.message) Let's pause and reset to keep you safe."
            
        case .technical:
            return "Movement analysis indicates risk: \(warning.message) Immediate form correction required."
            
        case .motivational:
            return "Hold up, warrior! \(warning.message) Let's fix this so you can keep crushing it safely!"
            
        case .safetyFirst:
            return "STOP! \(warning.message) Safety is our top priority. Let's address this immediately."
        }
    }
    
    // MARK: - Celebration Messages
    
    static func getCelebrationMessage(
        personality: CoachingPersonality,
        achievement: CelebrationContext,
        skillLevel: SkillLevel
    ) -> String {
        switch personality {
        case .supportive:
            return getSupportiveCelebration(achievement: achievement, skillLevel: skillLevel)
        case .technical:
            return getTechnicalCelebration(achievement: achievement, skillLevel: skillLevel)
        case .motivational:
            return getMotivationalCelebration(achievement: achievement, skillLevel: skillLevel)
        case .safetyFirst:
            return getSafetyFirstCelebration(achievement: achievement, skillLevel: skillLevel)
        }
    }
    
    private static func getSupportiveCelebration(achievement: CelebrationContext, skillLevel: SkillLevel) -> String {
        switch achievement {
        case .workoutComplete:
            return "You did it! I'm so proud of your effort today! You should feel amazing about what you just accomplished!"
        case .personalRecord:
            return "Incredible! You just set a personal record! Your dedication and hard work are truly inspiring!"
        case .perfectForm:
            return "That was absolutely beautiful! Your form was perfect! You should be so proud of your technique!"
        case .consistency:
            return "Your consistency is remarkable! Every workout you're showing up and getting better! That's so admirable!"
        }
    }
    
    private static func getTechnicalCelebration(achievement: CelebrationContext, skillLevel: SkillLevel) -> String {
        switch achievement {
        case .workoutComplete:
            return "Workout completed with excellent technical execution! Your movement patterns showed consistent quality throughout!"
        case .personalRecord:
            return "Personal record achieved through superior biomechanics! Your improved neuromuscular efficiency is evident!"
        case .perfectForm:
            return "Flawless movement execution! Your kinetic chain functioned optimally with perfect joint sequencing!"
        case .consistency:
            return "Consistent training demonstrates excellent motor learning adaptation! Your movement patterns are becoming automated!"
        }
    }
    
    private static func getMotivationalCelebration(achievement: CelebrationContext, skillLevel: SkillLevel) -> String {
        switch achievement {
        case .workoutComplete:
            return "WORKOUT COMPLETE! You absolutely DEMOLISHED that session! You're a FITNESS MACHINE!"
        case .personalRecord:
            return "PERSONAL RECORD SMASHED! You just proved you're UNSTOPPABLE! That's LEGENDARY performance!"
        case .perfectForm:
            return "FORM PERFECTION! You're operating like a PRECISION MACHINE! That's ELITE level execution!"
        case .consistency:
            return "CONSISTENCY CHAMPION! You're showing up every day like a TRUE WARRIOR! That's DEDICATION!"
        }
    }
    
    private static func getSafetyFirstCelebration(achievement: CelebrationContext, skillLevel: SkillLevel) -> String {
        switch achievement {
        case .workoutComplete:
            return "Excellent workout! You completed every exercise safely and effectively! Smart training pays off!"
        case .personalRecord:
            return "Safe personal record! You achieved this milestone without compromising form or safety! Well done!"
        case .perfectForm:
            return "Perfect form throughout! You're training smart and building sustainable strength!"
        case .consistency:
            return "Consistent, safe training! This approach will keep you healthy and strong for years to come!"
        }
    }
}

// MARK: - Supporting Enums

enum EncouragementContext {
    case goodForm
    case repCompletion
    case setCompletion
    case improvement
    case struggleSupport
}

enum RestContext {
    case betweenSets
    case betweenExercises
    case recovery
}

enum CelebrationContext {
    case workoutComplete
    case personalRecord
    case perfectForm
    case consistency
}

// MARK: - Coaching Settings

struct CoachingSettings: Codable {
    var isEnabled: Bool = true
    var personality: CoachingPersonality = .supportive
    var speechSettings: SpeechSettings = SpeechSettings()
    var hapticEnabled: Bool = true
    var visualFeedbackEnabled: Bool = true
    var feedbackFrequency: FeedbackFrequency = .normal
    var celebrationLevel: CelebrationLevel = .normal
    var safetyWarningsEnabled: Bool = true
    var encouragementEnabled: Bool = true
    var formCorrectionEnabled: Bool = true
    
    enum FeedbackFrequency: String, Codable, CaseIterable {
        case minimal = "Minimal"
        case normal = "Normal"
        case frequent = "Frequent"
        
        var cooldownMultiplier: Double {
            switch self {
            case .minimal: return 2.0
            case .normal: return 1.0
            case .frequent: return 0.5
            }
        }
    }
    
    enum CelebrationLevel: String, Codable, CaseIterable {
        case subtle = "Subtle"
        case normal = "Normal"
        case enthusiastic = "Enthusiastic"
        
        var intensity: Double {
            switch self {
            case .subtle: return 0.5
            case .normal: return 1.0
            case .enthusiastic: return 1.5
            }
        }
    }
}

// MARK: - Coaching Feedback Model

struct CoachingFeedback {
    let message: String
    let type: FeedbackType
    let priority: CoachingPriority
    let personality: CoachingPersonality
    let deliveryMethod: Set<DeliveryMethod>
    let timestamp: Date = Date()
    
    enum FeedbackType {
        case encouragement
        case formCorrection
        case safety
        case celebration
        case rest
        case information
    }
    
    enum DeliveryMethod {
        case speech
        case haptic
        case visual
    }
}