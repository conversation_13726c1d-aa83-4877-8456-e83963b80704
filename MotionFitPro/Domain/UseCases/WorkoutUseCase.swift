import Foundation

protocol WorkoutUseCaseProtocol {
    func getAvailableWorkouts() async throws -> [Workout]
    func getWorkoutById(_ id: UUID) async throws -> Workout?
    func createCustomWorkout(_ workout: Workout) async throws
    func updateWorkout(_ workout: Workout) async throws
    func deleteWorkout(_ id: UUID) async throws
    func getWorkoutRecommendations(for user: UserProfile) async throws -> [Workout]
    func searchWorkouts(query: String, filters: WorkoutFilters) async throws -> [Workout]
}

class WorkoutUseCase: WorkoutUseCaseProtocol {
    private let workoutRepository: WorkoutRepository
    private let userRepository: UserRepository
    private let logger = Logger.shared
    
    init(workoutRepository: WorkoutRepository, userRepository: UserRepository) {
        self.workoutRepository = workoutRepository
        self.userRepository = userRepository
    }
    
    func getAvailableWorkouts() async throws -> [Workout] {
        logger.info("Fetching available workouts", category: .workout)
        
        // In a real implementation, this would filter based on user subscription, device capabilities, etc.
        let allWorkouts = workoutRepository.workouts
        
        // Filter based on device capabilities
        let filteredWorkouts = allWorkouts.filter { workout in
            // Check if device supports required features
            return isWorkoutSupportedOnDevice(workout)
        }
        
        return filteredWorkouts
    }
    
    func getWorkoutById(_ id: UUID) async throws -> Workout? {
        return workoutRepository.workouts.first { $0.id == id }
    }
    
    func createCustomWorkout(_ workout: Workout) async throws {
        guard isValidWorkout(workout) else {
            throw MotionFitProError.invalidWorkoutConfiguration
        }
        
        logger.info("Creating custom workout: \(workout.name)", category: .workout)
        
        var customWorkout = workout
        customWorkout = Workout(
            id: workout.id,
            name: workout.name,
            description: workout.description,
            category: workout.category,
            difficulty: workout.difficulty,
            estimatedDuration: workout.estimatedDuration,
            exercises: workout.exercises,
            requiredEquipment: workout.requiredEquipment,
            targetMuscleGroups: workout.targetMuscleGroups,
            caloriesBurnedEstimate: calculateCalorieEstimate(for: workout),
            thumbnailImageName: workout.thumbnailImageName,
            videoPreviewURL: workout.videoPreviewURL,
            isCustom: true
        )
        
        try await workoutRepository.createCustomWorkout(customWorkout)
    }
    
    func updateWorkout(_ workout: Workout) async throws {
        guard workout.isCustom else {
            throw MotionFitProError.customError("Cannot modify pre-built workouts")
        }
        
        guard isValidWorkout(workout) else {
            throw MotionFitProError.invalidWorkoutConfiguration
        }
        
        logger.info("Updating custom workout: \(workout.name)", category: .workout)
        
        try await workoutRepository.createCustomWorkout(workout) // Using save method for updates
    }
    
    func deleteWorkout(_ id: UUID) async throws {
        guard let workout = try await getWorkoutById(id) else {
            throw MotionFitProError.workoutNotFound
        }
        
        try await workoutRepository.deleteWorkout(workout)
    }
    
    func getWorkoutRecommendations(for user: UserProfile) async throws -> [Workout] {
        let allWorkouts = try await getAvailableWorkouts()
        
        // Simple recommendation algorithm based on user preferences and history
        let recommendedWorkouts = allWorkouts.filter { workout in
            // Filter by difficulty level
            if let preferredDifficulty = user.preferredDifficulty {
                if workout.difficulty != preferredDifficulty {
                    return false
                }
            }
            
            // Filter by preferred workout types
            if !user.preferredWorkoutTypes.isEmpty {
                if !user.preferredWorkoutTypes.contains(workout.category) {
                    return false
                }
            }
            
            // Filter by available equipment
            let userEquipment = Set(user.availableEquipment)
            let requiredEquipment = Set(workout.requiredEquipment)
            if !requiredEquipment.isSubset(of: userEquipment) {
                return false
            }
            
            // Filter by time availability
            if let maxDuration = user.maxWorkoutDuration {
                if workout.estimatedDuration > maxDuration {
                    return false
                }
            }
            
            return true
        }
        
        // Sort by relevance score
        return recommendedWorkouts.sorted { workout1, workout2 in
            calculateRelevanceScore(workout1, for: user) > calculateRelevanceScore(workout2, for: user)
        }.prefix(10).map { $0 }
    }
    
    func searchWorkouts(query: String, filters: WorkoutFilters) async throws -> [Workout] {
        let allWorkouts = try await getAvailableWorkouts()
        
        let filteredWorkouts = allWorkouts.filter { workout in
            // Text search
            if !query.isEmpty {
                let searchText = query.lowercased()
                let matches = workout.name.lowercased().contains(searchText) ||
                             workout.description.lowercased().contains(searchText) ||
                             workout.exercises.contains { $0.name.lowercased().contains(searchText) }
                
                if !matches {
                    return false
                }
            }
            
            // Apply filters
            if let category = filters.category, workout.category != category {
                return false
            }
            
            if let difficulty = filters.difficulty, workout.difficulty != difficulty {
                return false
            }
            
            if let maxDuration = filters.maxDuration, workout.estimatedDuration > maxDuration {
                return false
            }
            
            if let minDuration = filters.minDuration, workout.estimatedDuration < minDuration {
                return false
            }
            
            if !filters.targetMuscleGroups.isEmpty {
                let workoutMuscles = Set(workout.targetMuscleGroups)
                let filterMuscles = Set(filters.targetMuscleGroups)
                if workoutMuscles.intersection(filterMuscles).isEmpty {
                    return false
                }
            }
            
            if !filters.requiredEquipment.isEmpty {
                let workoutEquipment = Set(workout.requiredEquipment)
                let filterEquipment = Set(filters.requiredEquipment)
                if !workoutEquipment.isSubset(of: filterEquipment) {
                    return false
                }
            }
            
            return true
        }
        
        return filteredWorkouts.sorted { workout1, workout2 in
            // Sort by relevance to search query
            if !query.isEmpty {
                let score1 = calculateSearchRelevance(workout1, query: query)
                let score2 = calculateSearchRelevance(workout2, query: query)
                return score1 > score2
            }
            
            // Default sort by popularity/rating
            return workout1.name < workout2.name
        }
    }
    
    // MARK: - Private Methods
    
    private func isWorkoutSupportedOnDevice(_ workout: Workout) -> Bool {
        // Check if device supports AR body tracking for exercises that require it
        let requiresBodyTracking = workout.exercises.contains { exercise in
            !exercise.requiredJoints.isEmpty
        }
        
        if requiresBodyTracking {
            return ARSessionManager.shared.isBodyTrackingSupported
        }
        
        return true
    }
    
    private func isValidWorkout(_ workout: Workout) -> Bool {
        // Basic validation
        guard !workout.name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return false
        }
        
        guard !workout.exercises.isEmpty else {
            return false
        }
        
        guard workout.estimatedDuration > 0 else {
            return false
        }
        
        // Validate exercises
        for exercise in workout.exercises {
            if exercise.defaultReps == nil && exercise.defaultDuration == nil {
                return false
            }
        }
        
        return true
    }
    
    private func calculateCalorieEstimate(for workout: Workout) -> Int {
        let baseCalories = workout.exercises.reduce(0) { total, exercise in
            let exerciseCalories: Double
            
            if let reps = exercise.defaultReps, let caloriesPerRep = exercise.caloriesPerRep {
                exerciseCalories = Double(reps * exercise.defaultSets) * caloriesPerRep
            } else if let duration = exercise.defaultDuration, let caloriesPerSecond = exercise.caloriesPerSecond {
                exerciseCalories = duration * Double(exercise.defaultSets) * caloriesPerSecond
            } else {
                exerciseCalories = 5.0 // Default estimate
            }
            
            return total + Int(exerciseCalories)
        }
        
        // Apply difficulty multiplier
        let difficultyMultiplier = workout.difficulty.multiplier
        
        return Int(Double(baseCalories) * difficultyMultiplier)
    }
    
    private func calculateRelevanceScore(_ workout: Workout, for user: UserProfile) -> Double {
        var score = 0.0
        
        // Preferred difficulty bonus
        if let preferredDifficulty = user.preferredDifficulty, workout.difficulty == preferredDifficulty {
            score += 10.0
        }
        
        // Preferred workout type bonus
        if user.preferredWorkoutTypes.contains(workout.category) {
            score += 15.0
        }
        
        // Target muscle groups alignment
        let userTargets = Set(user.targetMuscleGroups)
        let workoutTargets = Set(workout.targetMuscleGroups)
        let intersection = userTargets.intersection(workoutTargets)
        score += Double(intersection.count) * 5.0
        
        // Duration preference
        if let maxDuration = user.maxWorkoutDuration {
            let durationFactor = min(workout.estimatedDuration / maxDuration, 1.0)
            score += (1.0 - durationFactor) * 5.0
        }
        
        return score
    }
    
    private func calculateSearchRelevance(_ workout: Workout, query: String) -> Double {
        let searchText = query.lowercased()
        var score = 0.0
        
        // Exact name match gets highest score
        if workout.name.lowercased() == searchText {
            score += 100.0
        } else if workout.name.lowercased().contains(searchText) {
            score += 50.0
        }
        
        // Description match
        if workout.description.lowercased().contains(searchText) {
            score += 20.0
        }
        
        // Exercise name matches
        let exerciseMatches = workout.exercises.filter { $0.name.lowercased().contains(searchText) }
        score += Double(exerciseMatches.count) * 10.0
        
        return score
    }
}

// MARK: - Supporting Types

struct WorkoutFilters {
    let category: WorkoutCategory?
    let difficulty: DifficultyLevel?
    let maxDuration: TimeInterval?
    let minDuration: TimeInterval?
    let targetMuscleGroups: [MuscleGroup]
    let requiredEquipment: [Equipment]
    let includeCustomWorkouts: Bool
    let includePremiumWorkouts: Bool
    
    init(category: WorkoutCategory? = nil,
         difficulty: DifficultyLevel? = nil,
         maxDuration: TimeInterval? = nil,
         minDuration: TimeInterval? = nil,
         targetMuscleGroups: [MuscleGroup] = [],
         requiredEquipment: [Equipment] = [],
         includeCustomWorkouts: Bool = true,
         includePremiumWorkouts: Bool = true) {
        
        self.category = category
        self.difficulty = difficulty
        self.maxDuration = maxDuration
        self.minDuration = minDuration
        self.targetMuscleGroups = targetMuscleGroups
        self.requiredEquipment = requiredEquipment
        self.includeCustomWorkouts = includeCustomWorkouts
        self.includePremiumWorkouts = includePremiumWorkouts
    }
    
    static let empty = WorkoutFilters()
}

struct UserProfile {
    let id: UUID
    let name: String
    let age: Int?
    let weight: Double? // in kg
    let height: Double? // in cm
    let fitnessLevel: DifficultyLevel?
    let preferredDifficulty: DifficultyLevel?
    let preferredWorkoutTypes: [WorkoutCategory]
    let targetMuscleGroups: [MuscleGroup]
    let availableEquipment: [Equipment]
    let maxWorkoutDuration: TimeInterval?
    let fitnessGoals: [FitnessGoal]
    let createdAt: Date
    let updatedAt: Date
}

enum FitnessGoal: String, CaseIterable {
    case weightLoss = "Weight Loss"
    case muscleGain = "Muscle Gain"
    case endurance = "Endurance"
    case flexibility = "Flexibility"
    case strength = "Strength"
    case generalFitness = "General Fitness"
    case rehabilitation = "Rehabilitation"
}

// Placeholder for UserRepository
class UserRepository {
    static let shared = UserRepository()
    private init() {}
    
    func getCurrentUser() async throws -> UserProfile? {
        // Implementation would go here
        return nil
    }
}