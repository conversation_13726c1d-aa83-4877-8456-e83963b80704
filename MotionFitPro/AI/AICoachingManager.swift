import Foundation
import Combine

/// Intelligent AI coaching system that provides personalized feedback and progression
@MainActor
class AICoachingManager: ObservableObject {
    
    // MARK: - Published Properties
    @Published var currentCoachingMessage: String = ""
    @Published var motivationLevel: MotivationLevel = .neutral
    @Published var adaptiveRecommendations: [AdaptiveRecommendation] = []
    @Published var personalizedProgression: WorkoutProgression?
    @Published var coachingInsights: [CoachingInsight] = []
    @Published var isAnalyzing: Bool = false
    
    // MARK: - Private Properties
    private let dataController = DataController.shared
    private let mlProcessingManager = MLProcessingManager.shared
    private let audioManager = AudioManager.shared
    private let logger = Logger.shared
    
    private var cancellables = Set<AnyCancellable>()
    private var userProfile: UserProfile?
    private var workoutHistory: [WorkoutSession] = []
    private var performanceMetrics: PerformanceAnalytics = PerformanceAnalytics()
    private var coachingPersonality: CoachingPersonality = .encouraging
    
    // AI coaching parameters
    private let analysisWindow = 7 // Days to analyze for trends
    private let minWorkoutsForProgression = 3
    private let adaptationThreshold: Float = 0.15 // 15% improvement threshold
    
    // MARK: - Initialization
    init() {
        setupSubscriptions()
        loadUserData()
    }
    
    // MARK: - Public Interface
    
    /// Start AI coaching for a workout session
    func startCoaching(for exercise: ExerciseType, userProfile: UserProfile) {
        self.userProfile = userProfile
        self.coachingPersonality = userProfile.coachingPersonality
        isAnalyzing = true
        
        // Generate initial coaching message
        generateInitialCoachingMessage(for: exercise)
        
        // Analyze user's historical performance
        analyzeHistoricalPerformance(for: exercise)
        
        logger.info("AI coaching started for \(exercise)", category: .aiCoaching)
    }
    
    /// Stop AI coaching
    func stopCoaching() {
        isAnalyzing = false
        currentCoachingMessage = ""
        adaptiveRecommendations.removeAll()
        
        logger.info("AI coaching stopped", category: .aiCoaching)
    }
    
    /// Process workout performance and generate insights
    func processWorkoutPerformance(_ session: WorkoutSession) async {
        guard let profile = userProfile else { return }
        
        isAnalyzing = true
        defer { isAnalyzing = false }
        
        // Analyze performance trends
        let analysis = await analyzeWorkoutPerformance(session, userProfile: profile)
        
        // Generate adaptive recommendations
        let recommendations = generateAdaptiveRecommendations(from: analysis)
        
        // Update progression plan
        let progression = calculatePersonalizedProgression(from: analysis)
        
        await MainActor.run {
            self.adaptiveRecommendations = recommendations
            self.personalizedProgression = progression
            self.coachingInsights = analysis.insights
        }
        
        // Generate motivational message
        generateMotivationalMessage(from: analysis)
        
        logger.info("Processed workout performance and generated AI insights", category: .aiCoaching)
    }
    
    /// Generate real-time coaching feedback
    func generateRealTimeFeedback(formScore: Float, repCount: Int, exerciseType: ExerciseType) {
        let feedback = generateContextualFeedback(
            formScore: formScore,
            repCount: repCount,
            exerciseType: exerciseType,
            personality: coachingPersonality
        )
        
        if !feedback.isEmpty {
            currentCoachingMessage = feedback
            deliverCoachingMessage(feedback)
        }
    }
    
    /// Get personalized workout recommendations
    func getPersonalizedWorkoutRecommendations() async -> [WorkoutRecommendation] {
        guard let profile = userProfile else { return [] }
        
        // Analyze user's fitness level and preferences
        let fitnessAnalysis = analyzeFitnessLevel(profile)
        
        // Consider recent performance
        let performanceAnalysis = analyzeRecentPerformance()
        
        // Generate recommendations
        return generateWorkoutRecommendations(
            fitnessLevel: fitnessAnalysis,
            performance: performanceAnalysis,
            preferences: profile
        )
    }
    
    // MARK: - Private Methods
    
    private func setupSubscriptions() {
        // Subscribe to ML processing updates
        mlProcessingManager.$formScore
            .receive(on: DispatchQueue.main)
            .sink { [weak self] formScore in
                self?.handleFormScoreUpdate(formScore)
            }
            .store(in: &cancellables)
        
        mlProcessingManager.$repCount
            .receive(on: DispatchQueue.main)
            .sink { [weak self] repCount in
                self?.handleRepCountUpdate(repCount)
            }
            .store(in: &cancellables)
    }
    
    private func loadUserData() {
        Task {
            do {
                userProfile = try await dataController.fetchUserProfile()
                workoutHistory = try await dataController.fetchWorkoutSessions()
                
                if let profile = userProfile {
                    coachingPersonality = profile.coachingPersonality
                }
            } catch {
                logger.error("Failed to load user data for AI coaching: \(error)", category: .aiCoaching)
            }
        }
    }
    
    private func generateInitialCoachingMessage(for exercise: ExerciseType) {
        let messages = getInitialMessages(for: exercise, personality: coachingPersonality)
        currentCoachingMessage = messages.randomElement() ?? "Let's get started!"
        deliverCoachingMessage(currentCoachingMessage)
    }
    
    private func analyzeHistoricalPerformance(for exercise: ExerciseType) {
        let relevantWorkouts = workoutHistory.filter { session in
            session.exercises.contains { $0.type == exercise }
        }
        
        if relevantWorkouts.count >= minWorkoutsForProgression {
            let trends = calculatePerformanceTrends(relevantWorkouts)
            updateMotivationLevel(based: trends)
        }
    }
    
    private func analyzeWorkoutPerformance(_ session: WorkoutSession, userProfile: UserProfile) async -> PerformanceAnalysis {
        let analysis = PerformanceAnalysis()
        
        // Analyze completion rate
        let completionRate = Float(session.exercisesCompleted) / Float(session.exercises.count)
        analysis.completionRate = completionRate
        
        // Analyze duration efficiency
        let expectedDuration = calculateExpectedDuration(session.exercises, userProfile: userProfile)
        analysis.durationEfficiency = Float(expectedDuration / session.totalDuration)
        
        // Analyze form consistency
        analysis.formConsistency = calculateFormConsistency(session)
        
        // Generate insights
        analysis.insights = generateInsights(from: analysis)
        
        return analysis
    }
    
    private func generateAdaptiveRecommendations(from analysis: PerformanceAnalysis) -> [AdaptiveRecommendation] {
        var recommendations: [AdaptiveRecommendation] = []
        
        // Completion rate recommendations
        if analysis.completionRate < 0.8 {
            recommendations.append(AdaptiveRecommendation(
                type: .workloadAdjustment,
                title: "Reduce Workout Intensity",
                description: "Consider reducing the number of sets or reps to improve completion rate",
                priority: .high,
                estimatedImpact: .moderate
            ))
        }
        
        // Form consistency recommendations
        if analysis.formConsistency < 0.7 {
            recommendations.append(AdaptiveRecommendation(
                type: .techniqueImprovement,
                title: "Focus on Form",
                description: "Slow down movements and focus on proper technique",
                priority: .high,
                estimatedImpact: .high
            ))
        }
        
        // Duration efficiency recommendations
        if analysis.durationEfficiency < 0.8 {
            recommendations.append(AdaptiveRecommendation(
                type: .pacing,
                title: "Improve Workout Pacing",
                description: "Try to maintain consistent rest periods between sets",
                priority: .medium,
                estimatedImpact: .moderate
            ))
        }
        
        return recommendations
    }
    
    private func calculatePersonalizedProgression(from analysis: PerformanceAnalysis) -> WorkoutProgression {
        let progression = WorkoutProgression()
        
        // Base progression on performance metrics
        if analysis.completionRate > 0.9 && analysis.formConsistency > 0.8 {
            progression.recommendedAdjustment = .increase
            progression.adjustmentPercentage = 0.1 // 10% increase
        } else if analysis.completionRate < 0.7 || analysis.formConsistency < 0.6 {
            progression.recommendedAdjustment = .decrease
            progression.adjustmentPercentage = 0.15 // 15% decrease
        } else {
            progression.recommendedAdjustment = .maintain
            progression.adjustmentPercentage = 0.0
        }
        
        progression.reasoning = generateProgressionReasoning(analysis)
        
        return progression
    }
    
    private func generateMotivationalMessage(from analysis: PerformanceAnalysis) {
        let messages = getMotivationalMessages(
            completionRate: analysis.completionRate,
            formConsistency: analysis.formConsistency,
            personality: coachingPersonality
        )
        
        currentCoachingMessage = messages.randomElement() ?? "Great work!"
        deliverCoachingMessage(currentCoachingMessage)
        
        // Update motivation level
        if analysis.completionRate > 0.8 && analysis.formConsistency > 0.7 {
            motivationLevel = .high
        } else if analysis.completionRate > 0.6 && analysis.formConsistency > 0.5 {
            motivationLevel = .moderate
        } else {
            motivationLevel = .low
        }
    }
    
    private func generateContextualFeedback(formScore: Float, repCount: Int, exerciseType: ExerciseType, personality: CoachingPersonality) -> String {
        switch personality {
        case .encouraging:
            if formScore > 0.8 {
                return ["Excellent form!", "Perfect technique!", "You're crushing it!"].randomElement() ?? ""
            } else if formScore > 0.6 {
                return ["Good job!", "Keep it up!", "Nice work!"].randomElement() ?? ""
            } else {
                return ["Focus on form", "Take your time", "Quality over quantity"].randomElement() ?? ""
            }
            
        case .motivational:
            if formScore > 0.8 {
                return ["Beast mode activated!", "Unstoppable!", "Champion form!"].randomElement() ?? ""
            } else if formScore > 0.6 {
                return ["Push harder!", "You've got this!", "Don't give up!"].randomElement() ?? ""
            } else {
                return ["Fight through it!", "Mental toughness!", "Prove yourself!"].randomElement() ?? ""
            }
            
        case .analytical:
            if formScore > 0.8 {
                return ["Form score: \(Int(formScore * 100))% - Excellent", "Optimal movement pattern", "Biomechanically sound"].randomElement() ?? ""
            } else if formScore > 0.6 {
                return ["Form score: \(Int(formScore * 100))% - Good", "Minor adjustments needed", "Technique improving"].randomElement() ?? ""
            } else {
                return ["Form score: \(Int(formScore * 100))% - Focus needed", "Review movement pattern", "Prioritize technique"].randomElement() ?? ""
            }
            
        case .calm:
            if formScore > 0.8 {
                return ["Beautiful movement", "Flowing nicely", "Centered and strong"].randomElement() ?? ""
            } else if formScore > 0.6 {
                return ["Breathe and focus", "Find your rhythm", "Stay present"].randomElement() ?? ""
            } else {
                return ["Slow and steady", "Listen to your body", "Mindful movement"].randomElement() ?? ""
            }
        }
    }
    
    private func handleFormScoreUpdate(_ formScore: Float) {
        guard isAnalyzing, let exercise = mlProcessingManager.currentExercise else { return }
        
        generateRealTimeFeedback(
            formScore: formScore,
            repCount: mlProcessingManager.repCount,
            exerciseType: exercise
        )
    }
    
    private func handleRepCountUpdate(_ repCount: Int) {
        // Provide rep-specific encouragement
        if repCount > 0 && repCount % 5 == 0 {
            let encouragement = getRepMilestoneMessage(repCount, personality: coachingPersonality)
            currentCoachingMessage = encouragement
            deliverCoachingMessage(encouragement)
        }
    }
    
    private func deliverCoachingMessage(_ message: String) {
        audioManager.speak(message)
        logger.debug("AI coaching message: \(message)", category: .aiCoaching)
    }
    
    // MARK: - Helper Methods
    
    private func getInitialMessages(for exercise: ExerciseType, personality: CoachingPersonality) -> [String] {
        switch personality {
        case .encouraging:
            return ["Let's do this together!", "You've got this!", "Ready to crush this workout!"]
        case .motivational:
            return ["Time to dominate!", "Show me what you're made of!", "Let's break some limits!"]
        case .analytical:
            return ["Focus on proper form", "Let's optimize your technique", "Quality movement is key"]
        case .calm:
            return ["Take a deep breath", "Find your center", "Move with intention"]
        }
    }
    
    private func getMotivationalMessages(completionRate: Float, formConsistency: Float, personality: CoachingPersonality) -> [String] {
        if completionRate > 0.8 && formConsistency > 0.7 {
            switch personality {
            case .encouraging:
                return ["Outstanding workout!", "You're getting stronger!", "Incredible progress!"]
            case .motivational:
                return ["Absolute beast!", "Crushing your goals!", "Unstoppable force!"]
            case .analytical:
                return ["Excellent performance metrics", "Optimal workout execution", "Data shows great improvement"]
            case .calm:
                return ["Beautiful session", "Harmonious movement", "Well-balanced effort"]
            }
        } else {
            switch personality {
            case .encouraging:
                return ["Every workout counts!", "You're building strength!", "Progress takes time!"]
            case .motivational:
                return ["Champions are made in moments like this!", "Push through the challenge!", "Greatness requires sacrifice!"]
            case .analytical:
                return ["Room for optimization", "Focus areas identified", "Systematic improvement needed"]
            case .calm:
                return ["Be patient with yourself", "Growth is a journey", "Honor your effort"]
            }
        }
    }
    
    private func getRepMilestoneMessage(_ repCount: Int, personality: CoachingPersonality) -> String {
        switch personality {
        case .encouraging:
            return "Great job! \(repCount) reps completed!"
        case .motivational:
            return "\(repCount) reps down! Keep pushing!"
        case .analytical:
            return "Rep \(repCount) completed. Maintaining pace."
        case .calm:
            return "\(repCount) mindful repetitions. Well done."
        }
    }
    
    private func calculatePerformanceTrends(_ workouts: [WorkoutSession]) -> PerformanceTrends {
        // Implementation would analyze trends in workout data
        return PerformanceTrends()
    }
    
    private func updateMotivationLevel(based trends: PerformanceTrends) {
        // Implementation would update motivation based on trends
        motivationLevel = .moderate
    }
    
    private func calculateExpectedDuration(_ exercises: [ExerciseData], userProfile: UserProfile) -> TimeInterval {
        // Implementation would calculate expected workout duration
        return 1800 // 30 minutes default
    }
    
    private func calculateFormConsistency(_ session: WorkoutSession) -> Float {
        // Implementation would analyze form consistency throughout workout
        return 0.8
    }
    
    private func generateInsights(from analysis: PerformanceAnalysis) -> [CoachingInsight] {
        var insights: [CoachingInsight] = []
        
        if analysis.completionRate > 0.9 {
            insights.append(CoachingInsight(
                type: .achievement,
                title: "Excellent Completion Rate",
                description: "You completed \(Int(analysis.completionRate * 100))% of your workout. Consider increasing intensity.",
                actionable: true
            ))
        }
        
        if analysis.formConsistency < 0.7 {
            insights.append(CoachingInsight(
                type: .improvement,
                title: "Form Consistency",
                description: "Focus on maintaining proper form throughout the entire workout.",
                actionable: true
            ))
        }
        
        return insights
    }
    
    private func generateProgressionReasoning(_ analysis: PerformanceAnalysis) -> String {
        if analysis.completionRate > 0.9 && analysis.formConsistency > 0.8 {
            return "Your excellent completion rate and form consistency indicate you're ready for increased challenge."
        } else if analysis.completionRate < 0.7 {
            return "Lower completion rate suggests the current intensity may be too challenging."
        } else {
            return "Current performance indicates you should maintain this level while focusing on consistency."
        }
    }
    
    private func analyzeFitnessLevel(_ profile: UserProfile) -> FitnessAnalysis {
        // Implementation would analyze user's fitness level
        return FitnessAnalysis()
    }
    
    private func analyzeRecentPerformance() -> RecentPerformanceAnalysis {
        // Implementation would analyze recent workout performance
        return RecentPerformanceAnalysis()
    }
    
    private func generateWorkoutRecommendations(fitnessLevel: FitnessAnalysis, performance: RecentPerformanceAnalysis, preferences: UserProfile) -> [WorkoutRecommendation] {
        // Implementation would generate personalized workout recommendations
        return []
    }
}

// MARK: - Supporting Types

enum MotivationLevel {
    case low, neutral, moderate, high
    
    var description: String {
        switch self {
        case .low: return "Needs Encouragement"
        case .neutral: return "Neutral"
        case .moderate: return "Motivated"
        case .high: return "Highly Motivated"
        }
    }
}

struct AdaptiveRecommendation {
    let type: RecommendationType
    let title: String
    let description: String
    let priority: Priority
    let estimatedImpact: Impact
    
    enum RecommendationType {
        case workloadAdjustment
        case techniqueImprovement
        case pacing
        case recovery
        case progression
    }
    
    enum Priority {
        case low, medium, high
    }
    
    enum Impact {
        case low, moderate, high
    }
}

struct WorkoutProgression {
    var recommendedAdjustment: Adjustment = .maintain
    var adjustmentPercentage: Float = 0.0
    var reasoning: String = ""
    
    enum Adjustment {
        case increase, decrease, maintain
    }
}

struct CoachingInsight {
    let type: InsightType
    let title: String
    let description: String
    let actionable: Bool
    
    enum InsightType {
        case achievement
        case improvement
        case warning
        case suggestion
    }
}

struct PerformanceAnalysis {
    var completionRate: Float = 0.0
    var durationEfficiency: Float = 0.0
    var formConsistency: Float = 0.0
    var insights: [CoachingInsight] = []
}

struct PerformanceAnalytics {
    var averageFormScore: Float = 0.0
    var completionTrend: Float = 0.0
    var improvementRate: Float = 0.0
}

struct PerformanceTrends {
    var isImproving: Bool = false
    var trendDirection: Float = 0.0
}

struct FitnessAnalysis {
    var level: FitnessLevel = .beginner
    var strengths: [String] = []
    var weaknesses: [String] = []
}

struct RecentPerformanceAnalysis {
    var averageScore: Float = 0.0
    var consistency: Float = 0.0
    var trends: [String] = []
}

struct WorkoutRecommendation {
    let title: String
    let description: String
    let exercises: [ExerciseData]
    let estimatedDuration: TimeInterval
    let difficulty: Difficulty
}

// MARK: - Logger Category Extension
extension Logger.Category {
    static let aiCoaching = Logger.Category("aiCoaching")
}
