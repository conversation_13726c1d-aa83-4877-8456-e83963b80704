import AVFoundation
import Combine

@MainActor
class AudioManager: ObservableObject {
    static let shared = AudioManager()
    
    @Published var isSpeechEnabled = true
    @Published var isHapticsEnabled = true
    
    private init() {}
    
    func speak(_ text: String) {
        // Basic speech implementation
        print("Speaking: \(text)")
    }
    
    func playHapticFeedback() {
        // Basic haptic feedback
    }
}