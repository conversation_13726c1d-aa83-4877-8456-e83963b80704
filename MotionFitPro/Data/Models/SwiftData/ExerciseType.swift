import Foundation

/// An enumeration of all exercises that the app can recognize and track.
enum ExerciseType: String, CaseIterable, Codable {
    case squat
    case pushUp
    case lunge
    case jumpingJack
    case plank
    case unknown

    /// A user-friendly display name for the exercise.
    var displayName: String {
        switch self {
        case .squat: return "Squat"
        case .pushUp: return "Push-Up"
        case .lunge: return "Lunge"
        case .jumpingJack: return "Jumping Jack"
        case .plank: return "Plank"
        case .unknown: return "Unknown Exercise"
        }
    }

    /// A brief description of how to perform the exercise.
    var description: String {
        switch self {
        case .squat:
            return "Stand with feet shoulder-width apart, lower your body by bending your knees and hips."
        case .pushUp:
            return "Start in a plank position, lower your body until your chest nearly touches the floor."
        case .lunge:
            return "Step forward with one leg, lowering your hips until both knees are bent at about 90 degrees."
        case .jumpingJack:
            return "Jump to a position with legs spread wide and hands overhead, then return to starting position."
        case .plank:
            return "Hold a push-up position with your body in a straight line from head to heels."
        case .unknown:
            return "Exercise type not recognized."
        }
    }

    /// The primary muscle groups targeted by this exercise.
    var targetMuscles: [String] {
        switch self {
        case .squat:
            return ["Quadriceps", "Glutes", "Hamstrings", "Calves"]
        case .pushUp:
            return ["Chest", "Shoulders", "Triceps", "Core"]
        case .lunge:
            return ["Quadriceps", "Glutes", "Hamstrings", "Calves"]
        case .jumpingJack:
            return ["Cardiovascular", "Legs", "Arms"]
        case .plank:
            return ["Core", "Shoulders", "Back"]
        case .unknown:
            return []
        }
    }

    /// Estimated calories burned per minute for an average person.
    var caloriesPerMinute: Double {
        switch self {
        case .squat: return 8.0
        case .pushUp: return 7.0
        case .lunge: return 6.0
        case .jumpingJack: return 10.0
        case .plank: return 5.0
        case .unknown: return 0.0
        }
    }

    /// Difficulty level of the exercise (1-5 scale).
    var difficultyLevel: Int {
        switch self {
        case .squat: return 2
        case .pushUp: return 3
        case .lunge: return 3
        case .jumpingJack: return 1
        case .plank: return 2
        case .unknown: return 0
        }
    }
}

/// Coaching personality types for AI coaching
enum CoachingPersonality: String, CaseIterable, Codable {
    case encouraging = "encouraging"
    case strict = "strict"
    case analytical = "analytical"
    case motivational = "motivational"
    case gentle = "gentle"

    var displayName: String {
        switch self {
        case .encouraging: return "Encouraging"
        case .strict: return "Strict"
        case .analytical: return "Analytical"
        case .motivational: return "Motivational"
        case .gentle: return "Gentle"
        }
    }

    var description: String {
        switch self {
        case .encouraging:
            return "Positive reinforcement and supportive feedback"
        case .strict:
            return "Direct feedback with high standards"
        case .analytical:
            return "Data-driven insights and technical feedback"
        case .motivational:
            return "High-energy motivation and goal-focused coaching"
        case .gentle:
            return "Patient guidance with gentle corrections"
        }
    }
}
