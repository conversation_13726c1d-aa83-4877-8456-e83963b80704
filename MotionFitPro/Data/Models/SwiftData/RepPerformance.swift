import Foundation
import SwiftData

@Model
class RepPerformance {
    @Attribute(.unique) var id: UUID
    var setPerformanceID: UUID
    var repNumber: Int
    var startTime: Date
    var endTime: Date
    var duration: TimeInterval
    var formScore: Double
    var peakVelocity: Double
    var rangeOfMotion: Double
    var steadiness: Double
    var timing: Double
    var feedback: String?
    var status: RepStatus
    var createdDate: Date
    
    init(setPerformanceID: UUID, repNumber: Int, duration: TimeInterval, formScore: Double) {
        self.id = UUID()
        self.setPerformanceID = setPerformanceID
        self.repNumber = repNumber
        self.startTime = Date()
        self.endTime = Date().addingTimeInterval(duration)
        self.duration = duration
        self.formScore = formScore
        self.peakVelocity = 0.0
        self.rangeOfMotion = 0.0
        self.steadiness = 0.0
        self.timing = 0.0
        self.status = .completed
        self.createdDate = Date()
    }
}

enum RepStatus: String, CaseIterable, Codable {
    case completed = "completed"
    case incomplete = "incomplete"
    case failed = "failed"
}