import Foundation
import SwiftData

/// Coaching personality types for AI coaching
enum CoachingPersonality: String, CaseIterable, Codable {
    case encouraging = "encouraging"
    case strict = "strict"
    case analytical = "analytical"
    case motivational = "motivational"
    case gentle = "gentle"

    var displayName: String {
        switch self {
        case .encouraging: return "Encouraging"
        case .strict: return "Strict"
        case .analytical: return "Analytical"
        case .motivational: return "Motivational"
        case .gentle: return "Gentle"
        }
    }
}

@Model
class UserProfile {
    @Attribute(.unique) var id: UUID
    var name: String
    var age: Int
    var height: Double // in meters
    var weight: Double // in kg
    var fitnessLevel: FitnessLevel
    var preferredCoachingStyle: CoachingPersonality
    var createdDate: Date
    var lastModified: Date
    
    init(name: String, age: Int, height: Double, weight: Double, fitnessLevel: FitnessLevel, preferredCoachingStyle: CoachingPersonality) {
        self.id = UUID()
        self.name = name
        self.age = age
        self.height = height
        self.weight = weight
        self.fitnessLevel = fitnessLevel
        self.preferredCoachingStyle = preferredCoachingStyle
        self.createdDate = Date()
        self.lastModified = Date()
    }
}

enum FitnessLevel: String, CaseIterable, Codable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    case expert = "expert"
}