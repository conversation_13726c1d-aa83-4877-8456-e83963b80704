import Foundation
import SwiftData

@Model
class UserProfile {
    @Attribute(.unique) var id: UUID
    var name: String
    var age: Int
    var height: Double // in meters
    var weight: Double // in kg
    var fitnessLevel: FitnessLevel
    var preferredCoachingStyle: CoachingPersonality
    var createdDate: Date
    var lastModified: Date
    
    init(name: String, age: Int, height: Double, weight: Double, fitnessLevel: FitnessLevel, preferredCoachingStyle: CoachingPersonality) {
        self.id = UUID()
        self.name = name
        self.age = age
        self.height = height
        self.weight = weight
        self.fitnessLevel = fitnessLevel
        self.preferredCoachingStyle = preferredCoachingStyle
        self.createdDate = Date()
        self.lastModified = Date()
    }
}

enum FitnessLevel: String, CaseIterable, Codable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    case expert = "expert"
}