import Foundation
import SwiftData

@Model
class WorkoutSession {
    @Attribute(.unique) var id: UUID
    var userID: UUID
    var startTime: Date
    var endTime: Date?
    var totalDuration: TimeInterval
    var totalReps: Int
    var averageFormScore: Double
    var caloriesBurned: Double
    var workoutType: String
    var status: WorkoutStatus
    var notes: String?
    var createdDate: Date
    var lastModified: Date
    
    @Relationship(deleteRule: .cascade) var exercisePerformances: [ExercisePerformance] = []
    
    init(userID: UUID, workoutType: String) {
        self.id = UUID()
        self.userID = userID
        self.startTime = Date()
        self.totalDuration = 0
        self.totalReps = 0
        self.averageFormScore = 0.0
        self.caloriesBurned = 0.0
        self.workoutType = workoutType
        self.status = .inProgress
        self.createdDate = Date()
        self.lastModified = Date()
    }
}

enum WorkoutStatus: String, CaseIterable, Codable {
    case inProgress = "inProgress"
    case completed = "completed"
    case paused = "paused"
    case cancelled = "cancelled"
}