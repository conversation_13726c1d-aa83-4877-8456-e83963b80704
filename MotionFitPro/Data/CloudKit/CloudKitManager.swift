import Foundation
import CloudKit

class CloudKitManager: ObservableObject {
    static let shared = CloudKitManager()
    
    private let container: CKContainer
    private let publicDatabase: CKDatabase
    private let privateDatabase: CKDatabase
    
    private init() {
        container = CKContainer.default()
        publicDatabase = container.publicCloudDatabase
        privateDatabase = container.privateCloudDatabase
    }
    
    func checkAccountStatus() async -> CKAccountStatus {
        do {
            return try await container.accountStatus()
        } catch {
            print("Failed to check CloudKit account status: \(error)")
            return .couldNotDetermine
        }
    }
    
    func save(record: CKRecord) async throws -> CKRecord {
        return try await privateDatabase.save(record)
    }
    
    func fetch(recordID: CKRecord.ID) async throws -> CKRecord {
        return try await privateDatabase.record(for: recordID)
    }
    
    func delete(recordID: CKRecord.ID) async throws -> CKRecord.ID {
        return try await privateDatabase.deleteRecord(withID: recordID)
    }
}