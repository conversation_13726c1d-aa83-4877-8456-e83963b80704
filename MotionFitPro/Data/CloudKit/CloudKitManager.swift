import Foundation
import CloudKit
import Combine

@MainActor
class CloudKitManager: ObservableObject {
    static let shared = CloudKitManager()

    // MARK: - Published Properties
    @Published var accountStatus: CKAccountStatus = .couldNotDetermine
    @Published var isSyncing: Bool = false
    @Published var lastSyncDate: Date?
    @Published var syncError: Error?

    // MARK: - Private Properties
    private let container: CKContainer
    private let publicDatabase: CKDatabase
    private let privateDatabase: CKDatabase
    private let logger = Logger.shared
    private var cancellables = Set<AnyCancellable>()

    // Record types
    private enum RecordType {
        static let workoutSession = "WorkoutSession"
        static let userProfile = "UserProfile"
        static let achievement = "Achievement"
        static let personalRecord = "PersonalRecord"
        static let exercisePerformance = "ExercisePerformance"
    }

    // MARK: - Initialization
    private init() {
        container = CKContainer.default()
        publicDatabase = container.publicCloudDatabase
        privateDatabase = container.privateCloudDatabase

        Task {
            await checkAccountStatus()
            await setupSubscriptions()
        }
    }

    // MARK: - Account Management

    func checkAccountStatus() async {
        do {
            let status = try await container.accountStatus()
            await MainActor.run {
                self.accountStatus = status
                self.logger.info("CloudKit account status: \(status)", category: .cloudKit)
            }
        } catch {
            await MainActor.run {
                self.accountStatus = .couldNotDetermine
                self.syncError = error
                self.logger.error("Failed to check CloudKit account status: \(error)", category: .cloudKit)
            }
        }
    }

    func requestPermissions() async -> Bool {
        do {
            let status = try await container.requestApplicationPermission(.userDiscoverability)
            return status == .granted
        } catch {
            logger.error("Failed to request CloudKit permissions: \(error)", category: .cloudKit)
            return false
        }
    }

    // MARK: - Workout Session Sync

    func saveWorkoutSession(_ session: WorkoutSession) async throws {
        guard accountStatus == .available else {
            throw CloudKitError.accountNotAvailable
        }

        isSyncing = true
        defer { isSyncing = false }

        let record = createWorkoutSessionRecord(from: session)

        do {
            let savedRecord = try await privateDatabase.save(record)
            logger.info("Workout session saved to CloudKit: \(savedRecord.recordID)", category: .cloudKit)
        } catch {
            logger.error("Failed to save workout session: \(error)", category: .cloudKit)
            throw error
        }
    }

    func fetchWorkoutSessions() async throws -> [WorkoutSession] {
        guard accountStatus == .available else {
            throw CloudKitError.accountNotAvailable
        }

        isSyncing = true
        defer { isSyncing = false }

        let query = CKQuery(recordType: RecordType.workoutSession, predicate: NSPredicate(value: true))
        query.sortDescriptors = [NSSortDescriptor(key: "startTime", ascending: false)]

        do {
            let (matchResults, _) = try await privateDatabase.records(matching: query)
            var sessions: [WorkoutSession] = []

            for (_, result) in matchResults {
                switch result {
                case .success(let record):
                    if let session = createWorkoutSession(from: record) {
                        sessions.append(session)
                    }
                case .failure(let error):
                    logger.warning("Failed to process workout session record: \(error)", category: .cloudKit)
                }
            }

            logger.info("Fetched \(sessions.count) workout sessions from CloudKit", category: .cloudKit)
            return sessions
        } catch {
            logger.error("Failed to fetch workout sessions: \(error)", category: .cloudKit)
            throw error
        }
    }

    // MARK: - User Profile Sync

    func saveUserProfile(_ profile: UserProfile) async throws {
        guard accountStatus == .available else {
            throw CloudKitError.accountNotAvailable
        }

        isSyncing = true
        defer { isSyncing = false }

        let record = createUserProfileRecord(from: profile)

        do {
            let savedRecord = try await privateDatabase.save(record)
            logger.info("User profile saved to CloudKit: \(savedRecord.recordID)", category: .cloudKit)
        } catch {
            logger.error("Failed to save user profile: \(error)", category: .cloudKit)
            throw error
        }
    }

    func fetchUserProfile() async throws -> UserProfile? {
        guard accountStatus == .available else {
            throw CloudKitError.accountNotAvailable
        }

        isSyncing = true
        defer { isSyncing = false }

        let query = CKQuery(recordType: RecordType.userProfile, predicate: NSPredicate(value: true))

        do {
            let (matchResults, _) = try await privateDatabase.records(matching: query)

            for (_, result) in matchResults {
                switch result {
                case .success(let record):
                    if let profile = createUserProfile(from: record) {
                        logger.info("Fetched user profile from CloudKit", category: .cloudKit)
                        return profile
                    }
                case .failure(let error):
                    logger.warning("Failed to process user profile record: \(error)", category: .cloudKit)
                }
            }

            return nil
        } catch {
            logger.error("Failed to fetch user profile: \(error)", category: .cloudKit)
            throw error
        }
    }

    // MARK: - Achievement Sync

    func saveAchievement(_ achievement: Achievement) async throws {
        guard accountStatus == .available else {
            throw CloudKitError.accountNotAvailable
        }

        let record = createAchievementRecord(from: achievement)

        do {
            let savedRecord = try await privateDatabase.save(record)
            logger.info("Achievement saved to CloudKit: \(savedRecord.recordID)", category: .cloudKit)
        } catch {
            logger.error("Failed to save achievement: \(error)", category: .cloudKit)
            throw error
        }
    }

    func fetchAchievements() async throws -> [Achievement] {
        guard accountStatus == .available else {
            throw CloudKitError.accountNotAvailable
        }

        let query = CKQuery(recordType: RecordType.achievement, predicate: NSPredicate(value: true))

        do {
            let (matchResults, _) = try await privateDatabase.records(matching: query)
            var achievements: [Achievement] = []

            for (_, result) in matchResults {
                switch result {
                case .success(let record):
                    if let achievement = createAchievement(from: record) {
                        achievements.append(achievement)
                    }
                case .failure(let error):
                    logger.warning("Failed to process achievement record: \(error)", category: .cloudKit)
                }
            }

            logger.info("Fetched \(achievements.count) achievements from CloudKit", category: .cloudKit)
            return achievements
        } catch {
            logger.error("Failed to fetch achievements: \(error)", category: .cloudKit)
            throw error
        }
    }

    // MARK: - Batch Operations

    func syncAllData() async throws {
        guard accountStatus == .available else {
            throw CloudKitError.accountNotAvailable
        }

        isSyncing = true
        defer {
            isSyncing = false
            lastSyncDate = Date()
        }

        logger.info("Starting full CloudKit sync", category: .cloudKit)

        // Sync in order of dependencies
        try await syncUserProfile()
        try await syncWorkoutSessions()
        try await syncAchievements()
        try await syncPersonalRecords()

        logger.info("CloudKit sync completed successfully", category: .cloudKit)
    }

    private func syncUserProfile() async throws {
        // Implementation would sync local user profile with CloudKit
        logger.debug("Syncing user profile", category: .cloudKit)
    }

    private func syncWorkoutSessions() async throws {
        // Implementation would sync local workout sessions with CloudKit
        logger.debug("Syncing workout sessions", category: .cloudKit)
    }

    private func syncAchievements() async throws {
        // Implementation would sync local achievements with CloudKit
        logger.debug("Syncing achievements", category: .cloudKit)
    }

    private func syncPersonalRecords() async throws {
        // Implementation would sync local personal records with CloudKit
        logger.debug("Syncing personal records", category: .cloudKit)
    }

    // MARK: - Record Creation Methods

    private func createWorkoutSessionRecord(from session: WorkoutSession) -> CKRecord {
        let recordID = CKRecord.ID(recordName: session.id.uuidString)
        let record = CKRecord(recordType: RecordType.workoutSession, recordID: recordID)

        record["startTime"] = session.startTime
        record["endTime"] = session.endTime
        record["totalDuration"] = session.totalDuration
        record["exercisesCompleted"] = session.exercisesCompleted
        record["totalExercises"] = session.exercises.count

        // Encode exercises as JSON data
        if let exercisesData = try? JSONEncoder().encode(session.exercises) {
            record["exercisesData"] = exercisesData
        }

        // Encode settings as JSON data
        if let settingsData = try? JSONEncoder().encode(session.settings) {
            record["settingsData"] = settingsData
        }

        return record
    }

    private func createUserProfileRecord(from profile: UserProfile) -> CKRecord {
        let recordID = CKRecord.ID(recordName: profile.id.uuidString)
        let record = CKRecord(recordType: RecordType.userProfile, recordID: recordID)

        record["name"] = profile.name
        record["email"] = profile.email
        record["fitnessLevel"] = profile.fitnessLevel.rawValue
        record["preferredWorkoutDuration"] = profile.preferredWorkoutDuration.rawValue
        record["coachingPersonality"] = profile.coachingPersonality.rawValue
        record["memberSince"] = profile.memberSince

        return record
    }

    private func createAchievementRecord(from achievement: Achievement) -> CKRecord {
        let recordID = CKRecord.ID(recordName: achievement.id.uuidString)
        let record = CKRecord(recordType: RecordType.achievement, recordID: recordID)

        record["name"] = achievement.name
        record["description"] = achievement.description
        record["category"] = achievement.category.rawValue
        record["iconName"] = achievement.iconName
        record["unlockedDate"] = achievement.unlockedDate
        record["progress"] = achievement.progress
        record["target"] = achievement.target
        record["current"] = achievement.current
        record["isUnlocked"] = achievement.isUnlocked
        record["rarity"] = achievement.rarity.rawValue
        record["points"] = achievement.points

        return record
    }

    // MARK: - Record Conversion Methods

    private func createWorkoutSession(from record: CKRecord) -> WorkoutSession? {
        guard let startTime = record["startTime"] as? Date,
              let totalDuration = record["totalDuration"] as? TimeInterval,
              let exercisesCompleted = record["exercisesCompleted"] as? Int,
              let exercisesData = record["exercisesData"] as? Data,
              let settingsData = record["settingsData"] as? Data else {
            return nil
        }

        guard let exercises = try? JSONDecoder().decode([ExerciseData].self, from: exercisesData),
              let settings = try? JSONDecoder().decode(WorkoutSettings.self, from: settingsData) else {
            return nil
        }

        let endTime = record["endTime"] as? Date

        return WorkoutSession(
            id: UUID(uuidString: record.recordID.recordName) ?? UUID(),
            exercises: exercises,
            settings: settings,
            startTime: startTime,
            endTime: endTime,
            exercisesCompleted: exercisesCompleted,
            totalDuration: totalDuration
        )
    }

    private func createUserProfile(from record: CKRecord) -> UserProfile? {
        guard let name = record["name"] as? String,
              let email = record["email"] as? String,
              let fitnessLevelRaw = record["fitnessLevel"] as? String,
              let durationRaw = record["preferredWorkoutDuration"] as? String,
              let personalityRaw = record["coachingPersonality"] as? String,
              let memberSince = record["memberSince"] as? Date else {
            return nil
        }

        guard let fitnessLevel = FitnessLevel(rawValue: fitnessLevelRaw),
              let duration = WorkoutDuration(rawValue: durationRaw),
              let personality = CoachingPersonality(rawValue: personalityRaw) else {
            return nil
        }

        return UserProfile(
            id: UUID(uuidString: record.recordID.recordName) ?? UUID(),
            name: name,
            email: email,
            fitnessLevel: fitnessLevel,
            preferredWorkoutDuration: duration,
            coachingPersonality: personality,
            memberSince: memberSince
        )
    }

    private func createAchievement(from record: CKRecord) -> Achievement? {
        guard let name = record["name"] as? String,
              let description = record["description"] as? String,
              let categoryRaw = record["category"] as? String,
              let iconName = record["iconName"] as? String,
              let progress = record["progress"] as? Double,
              let target = record["target"] as? Double,
              let current = record["current"] as? Double,
              let isUnlocked = record["isUnlocked"] as? Bool,
              let rarityRaw = record["rarity"] as? String,
              let points = record["points"] as? Int else {
            return nil
        }

        guard let category = AchievementCategory(rawValue: categoryRaw),
              let rarity = AchievementRarity(rawValue: rarityRaw) else {
            return nil
        }

        let unlockedDate = record["unlockedDate"] as? Date

        return Achievement(
            id: UUID(uuidString: record.recordID.recordName) ?? UUID(),
            name: name,
            description: description,
            category: category,
            iconName: iconName,
            unlockedDate: unlockedDate,
            progress: progress,
            target: target,
            current: current,
            isUnlocked: isUnlocked,
            rarity: rarity,
            points: points
        )
    }
}

// MARK: - CloudKit Errors

enum CloudKitError: LocalizedError {
    case accountNotAvailable
    case networkUnavailable
    case quotaExceeded
    case conflictDetected
    case invalidData

    var errorDescription: String? {
        switch self {
        case .accountNotAvailable:
            return "CloudKit account is not available. Please sign in to iCloud."
        case .networkUnavailable:
            return "Network connection is not available."
        case .quotaExceeded:
            return "CloudKit storage quota exceeded."
        case .conflictDetected:
            return "Data conflict detected during sync."
        case .invalidData:
            return "Invalid data format for CloudKit sync."
        }
    }
}

// MARK: - Logger Category Extension
extension Logger.Category {
    static let cloudKit = Logger.Category("cloudKit")
}