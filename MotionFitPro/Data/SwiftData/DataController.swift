import Foundation
import SwiftData
import Combine

@MainActor
class DataController: ObservableObject {
    static let shared = DataController()

    // MARK: - Published Properties
    @Published var isSyncing: Bool = false
    @Published var lastSyncDate: Date?
    @Published var syncError: Error?

    // MARK: - Properties
    let container: ModelContainer
    private let cloudKitManager = CloudKitManager.shared
    private let logger = Logger.shared
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Initialization
    init() {
        let schema = Schema([
            UserProfile.self,
            WorkoutSession.self,
            ExercisePerformance.self,
            SetPerformance.self,
            RepPerformance.self
        ])

        let configuration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: false,
            cloudKitDatabase: .automatic
        )

        do {
            container = try ModelContainer(for: schema, configurations: [configuration])
            setupCloudKitSync()
        } catch {
            fatalError("Failed to create SwiftData container: \(error)")
        }
    }

    var context: ModelContext {
        return container.mainContext
    }

    // MARK: - Basic CRUD Operations

    func save() {
        do {
            try context.save()
            logger.debug("SwiftData context saved successfully", category: .data)
        } catch {
            logger.error("Failed to save SwiftData context: \(error)", category: .data)
            syncError = error
        }
    }

    func insert<T: PersistentModel>(_ model: T) {
        context.insert(model)
        save()
    }

    func delete<T: PersistentModel>(_ model: T) {
        context.delete(model)
        save()
    }

    func fetch<T: PersistentModel>(_ type: T.Type) throws -> [T] {
        let descriptor = FetchDescriptor<T>()
        return try context.fetch(descriptor)
    }

    func fetchFirst<T: PersistentModel>(_ type: T.Type) throws -> T? {
        let descriptor = FetchDescriptor<T>()
        descriptor.fetchLimit = 1
        return try context.fetch(descriptor).first
    }

    func count<T: PersistentModel>(_ type: T.Type) throws -> Int {
        let descriptor = FetchDescriptor<T>()
        return try context.fetchCount(descriptor)
    }

    // MARK: - Workout Session Operations

    func saveWorkoutSession(_ session: WorkoutSession) async {
        insert(session)

        // Sync to CloudKit if available
        if cloudKitManager.accountStatus == .available {
            do {
                try await cloudKitManager.saveWorkoutSession(session)
                logger.info("Workout session synced to CloudKit", category: .data)
            } catch {
                logger.error("Failed to sync workout session to CloudKit: \(error)", category: .data)
                syncError = error
            }
        }
    }

    func fetchWorkoutSessions() async throws -> [WorkoutSession] {
        // First try to get from local storage
        let localSessions = try fetch(WorkoutSession.self)

        // If CloudKit is available, sync and get latest
        if cloudKitManager.accountStatus == .available {
            do {
                let cloudSessions = try await cloudKitManager.fetchWorkoutSessions()
                // Merge logic would go here to combine local and cloud data
                logger.info("Fetched \(cloudSessions.count) sessions from CloudKit", category: .data)
                return mergeWorkoutSessions(local: localSessions, cloud: cloudSessions)
            } catch {
                logger.warning("Failed to fetch from CloudKit, using local data: \(error)", category: .data)
                return localSessions
            }
        }

        return localSessions
    }

    // MARK: - User Profile Operations

    func saveUserProfile(_ profile: UserProfile) async {
        insert(profile)

        // Sync to CloudKit if available
        if cloudKitManager.accountStatus == .available {
            do {
                try await cloudKitManager.saveUserProfile(profile)
                logger.info("User profile synced to CloudKit", category: .data)
            } catch {
                logger.error("Failed to sync user profile to CloudKit: \(error)", category: .data)
                syncError = error
            }
        }
    }

    func fetchUserProfile() async throws -> UserProfile? {
        // First try local storage
        let localProfile = try fetchFirst(UserProfile.self)

        // If CloudKit is available, check for updates
        if cloudKitManager.accountStatus == .available {
            do {
                if let cloudProfile = try await cloudKitManager.fetchUserProfile() {
                    // Use the most recent profile
                    if let local = localProfile {
                        return cloudProfile.memberSince > local.memberSince ? cloudProfile : local
                    } else {
                        return cloudProfile
                    }
                }
            } catch {
                logger.warning("Failed to fetch user profile from CloudKit: \(error)", category: .data)
            }
        }

        return localProfile
    }

    // MARK: - CloudKit Sync

    private func setupCloudKitSync() {
        // Subscribe to CloudKit sync status
        cloudKitManager.$isSyncing
            .receive(on: DispatchQueue.main)
            .assign(to: &$isSyncing)

        cloudKitManager.$lastSyncDate
            .receive(on: DispatchQueue.main)
            .assign(to: &$lastSyncDate)

        cloudKitManager.$syncError
            .receive(on: DispatchQueue.main)
            .assign(to: &$syncError)
    }

    func syncWithCloudKit() async {
        guard cloudKitManager.accountStatus == .available else {
            logger.warning("CloudKit not available for sync", category: .data)
            return
        }

        isSyncing = true
        defer { isSyncing = false }

        do {
            try await cloudKitManager.syncAllData()
            lastSyncDate = Date()
            logger.info("CloudKit sync completed successfully", category: .data)
        } catch {
            logger.error("CloudKit sync failed: \(error)", category: .data)
            syncError = error
        }
    }

    // MARK: - Data Merging

    private func mergeWorkoutSessions(local: [WorkoutSession], cloud: [WorkoutSession]) -> [WorkoutSession] {
        var merged: [UUID: WorkoutSession] = [:]

        // Add local sessions
        for session in local {
            merged[session.id] = session
        }

        // Add or update with cloud sessions
        for session in cloud {
            if let existing = merged[session.id] {
                // Use the most recent version
                if let cloudEndTime = session.endTime, let localEndTime = existing.endTime {
                    merged[session.id] = cloudEndTime > localEndTime ? session : existing
                } else {
                    merged[session.id] = session.endTime != nil ? session : existing
                }
            } else {
                merged[session.id] = session
            }
        }

        return Array(merged.values).sorted { $0.startTime > $1.startTime }
    }
}

// MARK: - Logger Category Extension
extension Logger.Category {
    static let data = Logger.Category("data")
}