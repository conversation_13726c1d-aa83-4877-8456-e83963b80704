import Foundation
import SwiftData

@MainActor
class DataController: ObservableObject {
    static let shared = DataController()
    
    let container: ModelContainer
    
    init() {
        let schema = Schema([
            UserProfile.self,
            WorkoutSession.self,
            ExercisePerformance.self,
            SetPerformance.self,
            RepPerformance.self
        ])
        
        let configuration = ModelConfiguration(
            schema: schema,
            isStoredInMemoryOnly: false
        )
        
        do {
            container = try ModelContainer(for: schema, configurations: [configuration])
        } catch {
            fatalError("Failed to create SwiftData container: \(error)")
        }
    }
    
    var context: ModelContext {
        return container.mainContext
    }
    
    func save() {
        do {
            try context.save()
        } catch {
            print("Failed to save SwiftData context: \(error)")
        }
    }
}