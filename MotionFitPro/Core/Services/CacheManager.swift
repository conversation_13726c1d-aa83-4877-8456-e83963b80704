import Foundation

/// Manages caching of exercise data, user preferences, and temporary data
@MainActor
class CacheManager: ObservableObject {
    static let shared = CacheManager()
    
    // MARK: - Properties
    private let fileManager = FileManager.default
    private let logger = Logger.shared
    private var memoryCache: [String: Any] = [:]
    private let cacheQueue = DispatchQueue(label: "com.motionfitpro.cache", qos: .utility)
    
    // Cache directories
    private lazy var cacheDirectory: URL = {
        let urls = fileManager.urls(for: .cachesDirectory, in: .userDomainMask)
        let cacheURL = urls[0].appendingPathComponent("MotionFitPro")
        
        // Create directory if it doesn't exist
        try? fileManager.createDirectory(at: cacheURL, withIntermediateDirectories: true)
        return cacheURL
    }()
    
    private lazy var exerciseDataDirectory: URL = {
        let url = cacheDirectory.appendingPathComponent("ExerciseData")
        try? fileManager.createDirectory(at: url, withIntermediateDirectories: true)
        return url
    }()
    
    private lazy var userDataDirectory: URL = {
        let url = cacheDirectory.appendingPathComponent("UserData")
        try? fileManager.createDirectory(at: url, withIntermediateDirectories: true)
        return url
    }()
    
    private lazy var tempDirectory: URL = {
        let url = cacheDirectory.appendingPathComponent("Temp")
        try? fileManager.createDirectory(at: url, withIntermediateDirectories: true)
        return url
    }()
    
    // Cache settings
    private let maxMemoryCacheSize = 50 // Maximum items in memory cache
    private let maxDiskCacheAge: TimeInterval = 7 * 24 * 60 * 60 // 7 days
    
    // MARK: - Initialization
    private init() {
        setupCacheDirectories()
        cleanupExpiredCache()
    }
    
    // MARK: - Public Interface
    
    /// Cache exercise data
    func cacheExerciseData(_ exercises: [ExerciseData]) async {
        await withCheckedContinuation { continuation in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                do {
                    let data = try JSONEncoder().encode(exercises)
                    let url = self.exerciseDataDirectory.appendingPathComponent("exercises.json")
                    try data.write(to: url)
                    
                    // Also cache in memory for quick access
                    await MainActor.run {
                        self.memoryCache["exercises"] = exercises
                        self.logger.info("Cached \(exercises.count) exercises", category: .cache)
                    }
                } catch {
                    await MainActor.run {
                        self.logger.error("Failed to cache exercise data: \(error)", category: .cache)
                    }
                }
                
                continuation.resume()
            }
        }
    }
    
    /// Retrieve cached exercise data
    func getCachedExerciseData() async -> [ExerciseData]? {
        // Check memory cache first
        if let exercises = memoryCache["exercises"] as? [ExerciseData] {
            return exercises
        }
        
        // Check disk cache
        return await withCheckedContinuation { continuation in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: nil)
                    return
                }
                
                do {
                    let url = self.exerciseDataDirectory.appendingPathComponent("exercises.json")
                    let data = try Data(contentsOf: url)
                    let exercises = try JSONDecoder().decode([ExerciseData].self, from: data)
                    
                    // Cache in memory for next time
                    await MainActor.run {
                        self.memoryCache["exercises"] = exercises
                    }
                    
                    continuation.resume(returning: exercises)
                } catch {
                    await MainActor.run {
                        self.logger.warning("Failed to load cached exercise data: \(error)", category: .cache)
                    }
                    continuation.resume(returning: nil)
                }
            }
        }
    }
    
    /// Cache user preferences
    func cacheUserPreferences<T: Codable>(_ preferences: T, key: String) async {
        await withCheckedContinuation { continuation in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                do {
                    let data = try JSONEncoder().encode(preferences)
                    let url = self.userDataDirectory.appendingPathComponent("\(key).json")
                    try data.write(to: url)
                    
                    // Cache in memory
                    await MainActor.run {
                        self.memoryCache[key] = preferences
                        self.logger.info("Cached user preferences for key: \(key)", category: .cache)
                    }
                } catch {
                    await MainActor.run {
                        self.logger.error("Failed to cache user preferences: \(error)", category: .cache)
                    }
                }
                
                continuation.resume()
            }
        }
    }
    
    /// Retrieve cached user preferences
    func getCachedUserPreferences<T: Codable>(key: String, type: T.Type) async -> T? {
        // Check memory cache first
        if let preferences = memoryCache[key] as? T {
            return preferences
        }
        
        // Check disk cache
        return await withCheckedContinuation { continuation in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: nil)
                    return
                }
                
                do {
                    let url = self.userDataDirectory.appendingPathComponent("\(key).json")
                    let data = try Data(contentsOf: url)
                    let preferences = try JSONDecoder().decode(T.self, from: data)
                    
                    // Cache in memory
                    await MainActor.run {
                        self.memoryCache[key] = preferences
                    }
                    
                    continuation.resume(returning: preferences)
                } catch {
                    continuation.resume(returning: nil)
                }
            }
        }
    }
    
    /// Cache temporary data (automatically cleaned up)
    func cacheTempData<T: Codable>(_ data: T, key: String, expirationTime: TimeInterval = 3600) async {
        await withCheckedContinuation { continuation in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                do {
                    let wrapper = TempDataWrapper(data: data, expirationDate: Date().addingTimeInterval(expirationTime))
                    let encodedData = try JSONEncoder().encode(wrapper)
                    let url = self.tempDirectory.appendingPathComponent("\(key).json")
                    try encodedData.write(to: url)
                    
                    await MainActor.run {
                        self.logger.info("Cached temp data for key: \(key)", category: .cache)
                    }
                } catch {
                    await MainActor.run {
                        self.logger.error("Failed to cache temp data: \(error)", category: .cache)
                    }
                }
                
                continuation.resume()
            }
        }
    }
    
    /// Retrieve temporary data
    func getTempData<T: Codable>(key: String, type: T.Type) async -> T? {
        return await withCheckedContinuation { continuation in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: nil)
                    return
                }
                
                do {
                    let url = self.tempDirectory.appendingPathComponent("\(key).json")
                    let data = try Data(contentsOf: url)
                    let wrapper = try JSONDecoder().decode(TempDataWrapper<T>.self, from: data)
                    
                    // Check if data has expired
                    if wrapper.expirationDate < Date() {
                        // Remove expired data
                        try? self.fileManager.removeItem(at: url)
                        continuation.resume(returning: nil)
                        return
                    }
                    
                    continuation.resume(returning: wrapper.data)
                } catch {
                    continuation.resume(returning: nil)
                }
            }
        }
    }
    
    /// Clear all cached data
    func clearAllCache() async {
        await withCheckedContinuation { continuation in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume()
                    return
                }
                
                // Clear memory cache
                await MainActor.run {
                    self.memoryCache.removeAll()
                }
                
                // Clear disk cache
                do {
                    try self.fileManager.removeItem(at: self.cacheDirectory)
                    self.setupCacheDirectories()
                    
                    await MainActor.run {
                        self.logger.info("Cleared all cache", category: .cache)
                    }
                } catch {
                    await MainActor.run {
                        self.logger.error("Failed to clear cache: \(error)", category: .cache)
                    }
                }
                
                continuation.resume()
            }
        }
    }
    
    /// Clear expired cache entries
    func clearExpiredCache() async {
        await withCheckedContinuation { continuation in
            cacheQueue.async { [weak self] in
                self?.cleanupExpiredCache()
                continuation.resume()
            }
        }
    }
    
    /// Get cache size information
    func getCacheSize() async -> CacheSize {
        return await withCheckedContinuation { continuation in
            cacheQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: CacheSize(totalSize: 0, exerciseDataSize: 0, userDataSize: 0, tempDataSize: 0))
                    return
                }
                
                let exerciseSize = self.getDirectorySize(self.exerciseDataDirectory)
                let userSize = self.getDirectorySize(self.userDataDirectory)
                let tempSize = self.getDirectorySize(self.tempDirectory)
                let totalSize = exerciseSize + userSize + tempSize
                
                let cacheSize = CacheSize(
                    totalSize: totalSize,
                    exerciseDataSize: exerciseSize,
                    userDataSize: userSize,
                    tempDataSize: tempSize
                )
                
                continuation.resume(returning: cacheSize)
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func setupCacheDirectories() {
        let directories = [cacheDirectory, exerciseDataDirectory, userDataDirectory, tempDirectory]
        
        for directory in directories {
            do {
                try fileManager.createDirectory(at: directory, withIntermediateDirectories: true)
            } catch {
                logger.error("Failed to create cache directory: \(error)", category: .cache)
            }
        }
    }
    
    private func cleanupExpiredCache() {
        // Clean up temp directory
        cleanupDirectory(tempDirectory)
        
        // Clean up old files in other directories
        cleanupOldFiles(in: exerciseDataDirectory)
        cleanupOldFiles(in: userDataDirectory)
        
        // Trim memory cache if needed
        if memoryCache.count > maxMemoryCacheSize {
            let keysToRemove = Array(memoryCache.keys.prefix(memoryCache.count - maxMemoryCacheSize))
            for key in keysToRemove {
                memoryCache.removeValue(forKey: key)
            }
        }
    }
    
    private func cleanupDirectory(_ directory: URL) {
        guard let enumerator = fileManager.enumerator(at: directory, includingPropertiesForKeys: [.contentModificationDateKey]) else {
            return
        }
        
        for case let fileURL as URL in enumerator {
            do {
                let resourceValues = try fileURL.resourceValues(forKeys: [.contentModificationDateKey])
                if let modificationDate = resourceValues.contentModificationDate,
                   Date().timeIntervalSince(modificationDate) > maxDiskCacheAge {
                    try fileManager.removeItem(at: fileURL)
                }
            } catch {
                logger.warning("Failed to cleanup cache file: \(error)", category: .cache)
            }
        }
    }
    
    private func cleanupOldFiles(in directory: URL) {
        cleanupDirectory(directory)
    }
    
    private func getDirectorySize(_ directory: URL) -> Int64 {
        guard let enumerator = fileManager.enumerator(at: directory, includingPropertiesForKeys: [.fileSizeKey]) else {
            return 0
        }
        
        var totalSize: Int64 = 0
        for case let fileURL as URL in enumerator {
            do {
                let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                totalSize += Int64(resourceValues.fileSize ?? 0)
            } catch {
                continue
            }
        }
        
        return totalSize
    }
}

// MARK: - Supporting Types

struct TempDataWrapper<T: Codable>: Codable {
    let data: T
    let expirationDate: Date
}

struct CacheSize {
    let totalSize: Int64
    let exerciseDataSize: Int64
    let userDataSize: Int64
    let tempDataSize: Int64
    
    var totalSizeFormatted: String {
        return ByteCountFormatter.string(fromByteCount: totalSize, countStyle: .file)
    }
    
    var exerciseDataSizeFormatted: String {
        return ByteCountFormatter.string(fromByteCount: exerciseDataSize, countStyle: .file)
    }
    
    var userDataSizeFormatted: String {
        return ByteCountFormatter.string(fromByteCount: userDataSize, countStyle: .file)
    }
    
    var tempDataSizeFormatted: String {
        return ByteCountFormatter.string(fromByteCount: tempDataSize, countStyle: .file)
    }
}

// MARK: - Logger Category Extension
extension Logger.Category {
    static let cache = Logger.Category("cache")
}
