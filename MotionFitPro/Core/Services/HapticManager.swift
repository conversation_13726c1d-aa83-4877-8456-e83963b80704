import UIKit
import CoreHaptics

/// Comprehensive haptic feedback manager for workout interactions
@MainActor
class HapticManager: ObservableObject {
    static let shared = HapticManager()

    // MARK: - Properties
    @Published var isHapticsEnabled = true
    @Published var hapticIntensity: Float = 1.0

    private var hapticEngine: CHHapticEngine?
    private let impactLight = UIImpactFeedbackGenerator(style: .light)
    private let impactMedium = UIImpactFeedbackGenerator(style: .medium)
    private let impactHeavy = UIImpactFeedbackGenerator(style: .heavy)
    private let notification = UINotificationFeedbackGenerator()
    private let selection = UISelectionFeedbackGenerator()

    private var isEngineRunning = false
    private let logger = Logger.shared

    // MARK: - Initialization
    private init() {
        setupHapticEngine()
        loadUserPreferences()
    }

    // MARK: - Setup
    private func setupHapticEngine() {
        guard CHHapticEngine.capabilitiesForHardware().supportsHaptics else {
            logger.info("Device does not support haptics", category: .haptics)
            return
        }

        do {
            hapticEngine = try CHHapticEngine()
            hapticEngine?.stoppedHandler = { [weak self] reason in
                self?.handleEngineStop(reason: reason)
            }
            hapticEngine?.resetHandler = { [weak self] in
                self?.handleEngineReset()
            }
        } catch {
            logger.error("Failed to create haptic engine: \(error)", category: .haptics)
        }
    }

    private func loadUserPreferences() {
        isHapticsEnabled = UserDefaults.standard.bool(forKey: "haptics_enabled")
        hapticIntensity = UserDefaults.standard.float(forKey: "haptic_intensity")

        if hapticIntensity == 0 {
            hapticIntensity = 1.0
        }
    }

    // MARK: - Public Interface

    /// Trigger haptic feedback for specific workout events
    func trigger(_ type: HapticType) {
        guard isHapticsEnabled else { return }

        switch type {
        case .repCompleted:
            playRepCompletedHaptic()
        case .setCompleted:
            playSetCompletedHaptic()
        case .workoutStarted:
            playWorkoutStartedHaptic()
        case .workoutCompleted:
            playWorkoutCompletedHaptic()
        case .formCorrection:
            playFormCorrectionHaptic()
        case .achievement:
            playAchievementHaptic()
        case .error:
            playErrorHaptic()
        case .selection:
            playSelectionHaptic()
        case .countdown:
            playCountdownHaptic()
        case .warning:
            playWarningHaptic()
        case .success:
            playSuccessHaptic()
        case .milestone:
            playMilestoneHaptic()
        }
    }

    /// Legacy methods for backward compatibility
    func triggerSuccess() {
        trigger(.success)
    }

    func triggerImpact() {
        trigger(.repCompleted)
    }

    // MARK: - Specific Haptic Implementations

    private func playRepCompletedHaptic() {
        impactMedium.impactOccurred(intensity: hapticIntensity)
    }

    private func playSetCompletedHaptic() {
        impactHeavy.impactOccurred(intensity: hapticIntensity)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.impactHeavy.impactOccurred(intensity: self?.hapticIntensity ?? 1.0)
        }
    }

    private func playWorkoutStartedHaptic() {
        impactMedium.impactOccurred(intensity: hapticIntensity)
    }

    private func playWorkoutCompletedHaptic() {
        notification.notificationOccurred(.success)

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
            self?.impactHeavy.impactOccurred(intensity: self?.hapticIntensity ?? 1.0)
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) { [weak self] in
            self?.impactHeavy.impactOccurred(intensity: self?.hapticIntensity ?? 1.0)
        }
    }

    private func playFormCorrectionHaptic() {
        impactLight.impactOccurred(intensity: hapticIntensity * 0.7)
    }

    private func playAchievementHaptic() {
        notification.notificationOccurred(.success)
    }

    private func playErrorHaptic() {
        notification.notificationOccurred(.error)
    }

    private func playSelectionHaptic() {
        selection.selectionChanged()
    }

    private func playCountdownHaptic() {
        impactMedium.impactOccurred(intensity: hapticIntensity * 0.8)
    }

    private func playWarningHaptic() {
        notification.notificationOccurred(.warning)
    }

    private func playSuccessHaptic() {
        notification.notificationOccurred(.success)
    }

    private func playMilestoneHaptic() {
        for i in 0..<3 {
            DispatchQueue.main.asyncAfter(deadline: .now() + Double(i) * 0.15) { [weak self] in
                self?.impactHeavy.impactOccurred(intensity: self?.hapticIntensity ?? 1.0)
            }
        }
    }

    // MARK: - Engine Management
    private func handleEngineStop(reason: CHHapticEngine.StoppedReason) {
        isEngineRunning = false
        logger.warning("Haptic engine stopped: \(reason)", category: .haptics)
    }

    private func handleEngineReset() {
        logger.info("Haptic engine reset", category: .haptics)
        isEngineRunning = false
    }
}

// MARK: - Haptic Types
enum HapticType {
    case repCompleted
    case setCompleted
    case workoutStarted
    case workoutCompleted
    case formCorrection
    case achievement
    case error
    case selection
    case countdown
    case warning
    case success
    case milestone
}

// MARK: - Logger Category Extension
extension Logger.Category {
    static let haptics = Logger.Category("haptics")
}
