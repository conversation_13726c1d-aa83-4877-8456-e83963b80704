import UIKit

/// A manager for providing haptic feedback.
class HapticManager {
    static let shared = HapticManager()

    private let successGenerator = UINotificationFeedbackGenerator()
    private let impactGenerator = UIImpactFeedbackGenerator(style: .medium)

    private init() {
        successGenerator.prepare()
        impactGenerator.prepare()
    }

    /// Triggers a success notification haptic.
    /// Call this when a user successfully completes a task, like a valid repetition.
    func triggerSuccess() {
        successGenerator.notificationOccurred(.success)
    }

    /// Triggers a medium-impact haptic.
    /// Call this to provide feedback for a significant interaction.
    func triggerImpact() {
        impactGenerator.impactOccurred()
    }
}
