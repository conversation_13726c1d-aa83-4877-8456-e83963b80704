import Foundation
import Combine
import Charts

/// Advanced analytics system for comprehensive workout and progress tracking
@MainActor
class AdvancedAnalyticsManager: ObservableObject {
    
    // MARK: - Published Properties
    @Published var workoutAnalytics: WorkoutAnalytics = WorkoutAnalytics()
    @Published var progressMetrics: ProgressMetrics = ProgressMetrics()
    @Published var performanceTrends: [PerformanceTrend] = []
    @Published var strengthAnalysis: StrengthAnalysis = StrengthAnalysis()
    @Published var consistencyMetrics: ConsistencyMetrics = ConsistencyMetrics()
    @Published var personalRecords: [PersonalRecord] = []
    @Published var isAnalyzing: Bool = false
    @Published var analyticsInsights: [AnalyticsInsight] = []
    
    // MARK: - Private Properties
    private let dataController = DataController.shared
    private let logger = Logger.shared
    private var cancellables = Set<AnyCancellable>()
    
    // Analytics configuration
    private let analysisWindow = 30 // Days to analyze
    private let trendCalculationPeriod = 7 // Days for trend calculation
    private let minWorkoutsForTrends = 5
    
    // MARK: - Initialization
    init() {
        setupSubscriptions()
        loadAnalyticsData()
    }
    
    // MARK: - Public Interface
    
    /// Generate comprehensive analytics report
    func generateAnalyticsReport(timeframe: AnalyticsTimeframe = .month) async -> AnalyticsReport {
        isAnalyzing = true
        defer { isAnalyzing = false }
        
        logger.info("Generating analytics report for \(timeframe)", category: .analytics)
        
        do {
            // Fetch workout data for timeframe
            let workoutSessions = try await fetchWorkoutSessions(for: timeframe)
            
            // Generate analytics
            let analytics = await analyzeWorkoutData(workoutSessions, timeframe: timeframe)
            
            // Update published properties
            await updateAnalyticsProperties(analytics)
            
            // Generate insights
            let insights = generateAnalyticsInsights(analytics)
            analyticsInsights = insights
            
            let report = AnalyticsReport(
                timeframe: timeframe,
                workoutAnalytics: analytics.workoutAnalytics,
                progressMetrics: analytics.progressMetrics,
                performanceTrends: analytics.performanceTrends,
                strengthAnalysis: analytics.strengthAnalysis,
                consistencyMetrics: analytics.consistencyMetrics,
                insights: insights,
                generatedDate: Date()
            )
            
            logger.info("Analytics report generated successfully", category: .analytics)
            return report
            
        } catch {
            logger.error("Failed to generate analytics report: \(error)", category: .analytics)
            return AnalyticsReport.empty(timeframe: timeframe)
        }
    }
    
    /// Get performance trends for specific exercise
    func getExercisePerformanceTrends(_ exerciseType: ExerciseType, timeframe: AnalyticsTimeframe) async -> [ExercisePerformanceTrend] {
        do {
            let workoutSessions = try await fetchWorkoutSessions(for: timeframe)
            return analyzeExercisePerformance(exerciseType, in: workoutSessions)
        } catch {
            logger.error("Failed to get exercise performance trends: \(error)", category: .analytics)
            return []
        }
    }
    
    /// Calculate strength progression over time
    func calculateStrengthProgression(timeframe: AnalyticsTimeframe) async -> StrengthProgression {
        do {
            let workoutSessions = try await fetchWorkoutSessions(for: timeframe)
            return analyzeStrengthProgression(workoutSessions)
        } catch {
            logger.error("Failed to calculate strength progression: \(error)", category: .analytics)
            return StrengthProgression()
        }
    }
    
    /// Get workout consistency analysis
    func getConsistencyAnalysis(timeframe: AnalyticsTimeframe) async -> ConsistencyAnalysis {
        do {
            let workoutSessions = try await fetchWorkoutSessions(for: timeframe)
            return analyzeWorkoutConsistency(workoutSessions, timeframe: timeframe)
        } catch {
            logger.error("Failed to get consistency analysis: \(error)", category: .analytics)
            return ConsistencyAnalysis()
        }
    }
    
    /// Generate personalized recommendations based on analytics
    func generatePersonalizedRecommendations() async -> [PersonalizedRecommendation] {
        let analytics = await generateAnalyticsReport()
        return generateRecommendations(from: analytics)
    }
    
    /// Export analytics data
    func exportAnalyticsData(format: ExportFormat, timeframe: AnalyticsTimeframe) async -> Data? {
        do {
            let report = await generateAnalyticsReport(timeframe: timeframe)
            return try exportData(report, format: format)
        } catch {
            logger.error("Failed to export analytics data: \(error)", category: .analytics)
            return nil
        }
    }
    
    // MARK: - Private Methods
    
    private func setupSubscriptions() {
        // Subscribe to data updates
        dataController.$lastSyncDate
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.refreshAnalytics()
            }
            .store(in: &cancellables)
    }
    
    private func loadAnalyticsData() {
        Task {
            await refreshAnalytics()
        }
    }
    
    private func refreshAnalytics() {
        Task {
            let _ = await generateAnalyticsReport()
        }
    }
    
    private func fetchWorkoutSessions(for timeframe: AnalyticsTimeframe) async throws -> [WorkoutSession] {
        let sessions = try await dataController.fetchWorkoutSessions()
        let cutoffDate = Calendar.current.date(byAdding: timeframe.dateComponent, value: -timeframe.value, to: Date()) ?? Date()
        
        return sessions.filter { $0.startTime >= cutoffDate }
    }
    
    private func analyzeWorkoutData(_ sessions: [WorkoutSession], timeframe: AnalyticsTimeframe) async -> AnalyticsData {
        var data = AnalyticsData()
        
        // Workout Analytics
        data.workoutAnalytics = calculateWorkoutAnalytics(sessions)
        
        // Progress Metrics
        data.progressMetrics = calculateProgressMetrics(sessions, timeframe: timeframe)
        
        // Performance Trends
        data.performanceTrends = calculatePerformanceTrends(sessions)
        
        // Strength Analysis
        data.strengthAnalysis = calculateStrengthAnalysis(sessions)
        
        // Consistency Metrics
        data.consistencyMetrics = calculateConsistencyMetrics(sessions, timeframe: timeframe)
        
        return data
    }
    
    private func calculateWorkoutAnalytics(_ sessions: [WorkoutSession]) -> WorkoutAnalytics {
        var analytics = WorkoutAnalytics()
        
        analytics.totalWorkouts = sessions.count
        analytics.totalDuration = sessions.reduce(0) { $0 + $1.totalDuration }
        analytics.averageDuration = analytics.totalWorkouts > 0 ? analytics.totalDuration / Double(analytics.totalWorkouts) : 0
        
        // Calculate completion rates
        let completedWorkouts = sessions.filter { $0.exercisesCompleted == $0.exercises.count }
        analytics.completionRate = Double(completedWorkouts.count) / Double(max(sessions.count, 1))
        
        // Exercise type distribution
        var exerciseTypeCounts: [ExerciseType: Int] = [:]
        for session in sessions {
            for exercise in session.exercises {
                exerciseTypeCounts[exercise.type, default: 0] += 1
            }
        }
        analytics.exerciseTypeDistribution = exerciseTypeCounts
        
        // Most frequent exercises
        analytics.mostFrequentExercises = exerciseTypeCounts.sorted { $0.value > $1.value }
            .prefix(5)
            .map { $0.key }
        
        return analytics
    }
    
    private func calculateProgressMetrics(_ sessions: [WorkoutSession], timeframe: AnalyticsTimeframe) -> ProgressMetrics {
        var metrics = ProgressMetrics()
        
        guard sessions.count >= 2 else { return metrics }
        
        let sortedSessions = sessions.sorted { $0.startTime < $1.startTime }
        let firstHalf = Array(sortedSessions.prefix(sortedSessions.count / 2))
        let secondHalf = Array(sortedSessions.suffix(sortedSessions.count / 2))
        
        // Calculate improvement metrics
        let firstHalfAvgDuration = firstHalf.reduce(0) { $0 + $1.totalDuration } / Double(max(firstHalf.count, 1))
        let secondHalfAvgDuration = secondHalf.reduce(0) { $0 + $1.totalDuration } / Double(max(secondHalf.count, 1))
        
        metrics.durationImprovement = (secondHalfAvgDuration - firstHalfAvgDuration) / firstHalfAvgDuration
        
        // Calculate completion rate improvement
        let firstHalfCompletionRate = Double(firstHalf.filter { $0.exercisesCompleted == $0.exercises.count }.count) / Double(max(firstHalf.count, 1))
        let secondHalfCompletionRate = Double(secondHalf.filter { $0.exercisesCompleted == $0.exercises.count }.count) / Double(max(secondHalf.count, 1))
        
        metrics.completionRateImprovement = secondHalfCompletionRate - firstHalfCompletionRate
        
        // Calculate streak
        metrics.currentStreak = calculateCurrentStreak(sessions)
        metrics.longestStreak = calculateLongestStreak(sessions)
        
        return metrics
    }
    
    private func calculatePerformanceTrends(_ sessions: [WorkoutSession]) -> [PerformanceTrend] {
        var trends: [PerformanceTrend] = []
        
        // Group sessions by week
        let calendar = Calendar.current
        let groupedSessions = Dictionary(grouping: sessions) { session in
            calendar.dateInterval(of: .weekOfYear, for: session.startTime)?.start ?? session.startTime
        }
        
        let sortedWeeks = groupedSessions.keys.sorted()
        
        for week in sortedWeeks {
            guard let weekSessions = groupedSessions[week] else { continue }
            
            let avgDuration = weekSessions.reduce(0) { $0 + $1.totalDuration } / Double(weekSessions.count)
            let completionRate = Double(weekSessions.filter { $0.exercisesCompleted == $0.exercises.count }.count) / Double(weekSessions.count)
            
            trends.append(PerformanceTrend(
                date: week,
                averageDuration: avgDuration,
                completionRate: completionRate,
                workoutCount: weekSessions.count
            ))
        }
        
        return trends
    }
    
    private func calculateStrengthAnalysis(_ sessions: [WorkoutSession]) -> StrengthAnalysis {
        var analysis = StrengthAnalysis()
        
        // Analyze by muscle groups
        var muscleGroupFrequency: [MuscleGroup: Int] = [:]
        var muscleGroupProgress: [MuscleGroup: Double] = [:]
        
        for session in sessions {
            for exercise in session.exercises {
                for muscleGroup in exercise.primaryMuscles {
                    muscleGroupFrequency[muscleGroup, default: 0] += 1
                }
            }
        }
        
        analysis.muscleGroupDistribution = muscleGroupFrequency
        analysis.strongestMuscleGroups = muscleGroupFrequency.sorted { $0.value > $1.value }
            .prefix(3)
            .map { $0.key }
        
        // Identify weak points (least trained muscle groups)
        analysis.weakPoints = muscleGroupFrequency.sorted { $0.value < $1.value }
            .prefix(3)
            .map { $0.key }
        
        return analysis
    }
    
    private func calculateConsistencyMetrics(_ sessions: [WorkoutSession], timeframe: AnalyticsTimeframe) -> ConsistencyMetrics {
        var metrics = ConsistencyMetrics()
        
        let calendar = Calendar.current
        let now = Date()
        let startDate = calendar.date(byAdding: timeframe.dateComponent, value: -timeframe.value, to: now) ?? now
        
        // Calculate workout frequency
        let totalDays = calendar.dateComponents([.day], from: startDate, to: now).day ?? 1
        metrics.workoutFrequency = Double(sessions.count) / Double(totalDays) * 7 // Per week
        
        // Calculate consistency score (how evenly distributed workouts are)
        let workoutDates = sessions.map { calendar.startOfDay(for: $0.startTime) }
        let uniqueDates = Set(workoutDates)
        metrics.consistencyScore = Double(uniqueDates.count) / Double(max(sessions.count, 1))
        
        // Calculate rest day patterns
        let sortedDates = Array(uniqueDates).sorted()
        var restDayGaps: [Int] = []
        
        for i in 1..<sortedDates.count {
            let gap = calendar.dateComponents([.day], from: sortedDates[i-1], to: sortedDates[i]).day ?? 0
            if gap > 1 {
                restDayGaps.append(gap - 1)
            }
        }
        
        metrics.averageRestDays = restDayGaps.isEmpty ? 0 : Double(restDayGaps.reduce(0, +)) / Double(restDayGaps.count)
        
        return metrics
    }
    
    private func analyzeExercisePerformance(_ exerciseType: ExerciseType, in sessions: [WorkoutSession]) -> [ExercisePerformanceTrend] {
        var trends: [ExercisePerformanceTrend] = []
        
        for session in sessions.sorted(by: { $0.startTime < $1.startTime }) {
            let exerciseData = session.exercises.filter { $0.type == exerciseType }
            
            if !exerciseData.isEmpty {
                let avgReps = exerciseData.reduce(0) { $0 + $1.targetReps } / exerciseData.count
                let avgSets = exerciseData.reduce(0) { $0 + $1.targetSets } / exerciseData.count
                
                trends.append(ExercisePerformanceTrend(
                    date: session.startTime,
                    exerciseType: exerciseType,
                    averageReps: avgReps,
                    averageSets: avgSets,
                    volume: avgReps * avgSets
                ))
            }
        }
        
        return trends
    }
    
    private func analyzeStrengthProgression(_ sessions: [WorkoutSession]) -> StrengthProgression {
        var progression = StrengthProgression()
        
        // Calculate overall strength trend
        let sortedSessions = sessions.sorted { $0.startTime < $1.startTime }
        
        if sortedSessions.count >= 2 {
            let firstSession = sortedSessions.first!
            let lastSession = sortedSessions.last!
            
            let firstVolume = calculateTotalVolume(firstSession)
            let lastVolume = calculateTotalVolume(lastSession)
            
            progression.overallImprovement = (lastVolume - firstVolume) / firstVolume
            progression.timespan = lastSession.startTime.timeIntervalSince(firstSession.startTime)
        }
        
        return progression
    }
    
    private func analyzeWorkoutConsistency(_ sessions: [WorkoutSession], timeframe: AnalyticsTimeframe) -> ConsistencyAnalysis {
        var analysis = ConsistencyAnalysis()
        
        let calendar = Calendar.current
        let now = Date()
        let startDate = calendar.date(byAdding: timeframe.dateComponent, value: -timeframe.value, to: now) ?? now
        
        // Calculate weekly consistency
        var weeklyWorkouts: [Date: Int] = [:]
        
        for session in sessions {
            let weekStart = calendar.dateInterval(of: .weekOfYear, for: session.startTime)?.start ?? session.startTime
            weeklyWorkouts[weekStart, default: 0] += 1
        }
        
        let workoutCounts = Array(weeklyWorkouts.values)
        if !workoutCounts.isEmpty {
            analysis.averageWeeklyWorkouts = Double(workoutCounts.reduce(0, +)) / Double(workoutCounts.count)
            analysis.consistencyVariance = calculateVariance(workoutCounts.map { Double($0) })
        }
        
        return analysis
    }
    
    private func generateAnalyticsInsights(_ analytics: AnalyticsData) -> [AnalyticsInsight] {
        var insights: [AnalyticsInsight] = []
        
        // Completion rate insights
        if analytics.workoutAnalytics.completionRate > 0.9 {
            insights.append(AnalyticsInsight(
                type: .achievement,
                title: "Excellent Completion Rate",
                description: "You complete \(Int(analytics.workoutAnalytics.completionRate * 100))% of your workouts!",
                priority: .high,
                actionable: false
            ))
        } else if analytics.workoutAnalytics.completionRate < 0.7 {
            insights.append(AnalyticsInsight(
                type: .improvement,
                title: "Completion Rate Opportunity",
                description: "Consider adjusting workout intensity to improve completion rate.",
                priority: .medium,
                actionable: true
            ))
        }
        
        // Consistency insights
        if analytics.consistencyMetrics.workoutFrequency < 2.0 {
            insights.append(AnalyticsInsight(
                type: .suggestion,
                title: "Increase Workout Frequency",
                description: "Try to workout at least 3 times per week for better results.",
                priority: .medium,
                actionable: true
            ))
        }
        
        // Strength balance insights
        if analytics.strengthAnalysis.weakPoints.count > 0 {
            let weakPoint = analytics.strengthAnalysis.weakPoints.first!
            insights.append(AnalyticsInsight(
                type: .suggestion,
                title: "Address Muscle Imbalance",
                description: "Consider adding more \(weakPoint.displayName) exercises to your routine.",
                priority: .medium,
                actionable: true
            ))
        }
        
        return insights
    }
    
    private func generateRecommendations(from report: AnalyticsReport) -> [PersonalizedRecommendation] {
        var recommendations: [PersonalizedRecommendation] = []
        
        // Based on completion rate
        if report.workoutAnalytics.completionRate < 0.8 {
            recommendations.append(PersonalizedRecommendation(
                title: "Adjust Workout Intensity",
                description: "Consider reducing workout difficulty to improve completion rates",
                category: .workoutAdjustment,
                priority: .high,
                estimatedImpact: .high
            ))
        }
        
        // Based on consistency
        if report.consistencyMetrics.workoutFrequency < 3.0 {
            recommendations.append(PersonalizedRecommendation(
                title: "Increase Workout Frequency",
                description: "Aim for at least 3 workouts per week for optimal progress",
                category: .frequency,
                priority: .medium,
                estimatedImpact: .high
            ))
        }
        
        return recommendations
    }
    
    private func updateAnalyticsProperties(_ analytics: AnalyticsData) async {
        workoutAnalytics = analytics.workoutAnalytics
        progressMetrics = analytics.progressMetrics
        performanceTrends = analytics.performanceTrends
        strengthAnalysis = analytics.strengthAnalysis
        consistencyMetrics = analytics.consistencyMetrics
    }
    
    private func exportData(_ report: AnalyticsReport, format: ExportFormat) throws -> Data {
        switch format {
        case .json:
            return try JSONEncoder().encode(report)
        case .csv:
            return try generateCSVData(report)
        }
    }
    
    private func generateCSVData(_ report: AnalyticsReport) throws -> Data {
        // Implementation would generate CSV format
        let csvString = "Date,Workouts,Duration,Completion Rate\n"
        return csvString.data(using: .utf8) ?? Data()
    }
    
    // MARK: - Helper Methods
    
    private func calculateCurrentStreak(_ sessions: [WorkoutSession]) -> Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        var streak = 0
        var currentDate = today
        
        let workoutDates = Set(sessions.map { calendar.startOfDay(for: $0.startTime) })
        
        while workoutDates.contains(currentDate) || calendar.isDate(currentDate, inSameDayAs: today) {
            if workoutDates.contains(currentDate) {
                streak += 1
            }
            currentDate = calendar.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
        }
        
        return streak
    }
    
    private func calculateLongestStreak(_ sessions: [WorkoutSession]) -> Int {
        let calendar = Calendar.current
        let workoutDates = Set(sessions.map { calendar.startOfDay(for: $0.startTime) }).sorted()
        
        var longestStreak = 0
        var currentStreak = 0
        var previousDate: Date?
        
        for date in workoutDates {
            if let prev = previousDate, calendar.dateComponents([.day], from: prev, to: date).day == 1 {
                currentStreak += 1
            } else {
                currentStreak = 1
            }
            
            longestStreak = max(longestStreak, currentStreak)
            previousDate = date
        }
        
        return longestStreak
    }
    
    private func calculateTotalVolume(_ session: WorkoutSession) -> Double {
        return session.exercises.reduce(0) { total, exercise in
            total + Double(exercise.targetReps * exercise.targetSets)
        }
    }
    
    private func calculateVariance(_ values: [Double]) -> Double {
        guard !values.isEmpty else { return 0 }
        
        let mean = values.reduce(0, +) / Double(values.count)
        let squaredDifferences = values.map { pow($0 - mean, 2) }
        return squaredDifferences.reduce(0, +) / Double(values.count)
    }
}

// MARK: - Supporting Types

enum AnalyticsTimeframe {
    case week, month, quarter, year
    
    var value: Int {
        switch self {
        case .week: return 1
        case .month: return 1
        case .quarter: return 3
        case .year: return 1
        }
    }
    
    var dateComponent: Calendar.Component {
        switch self {
        case .week: return .weekOfYear
        case .month: return .month
        case .quarter: return .month
        case .year: return .year
        }
    }
}

enum ExportFormat {
    case json, csv
}

struct AnalyticsData {
    var workoutAnalytics = WorkoutAnalytics()
    var progressMetrics = ProgressMetrics()
    var performanceTrends: [PerformanceTrend] = []
    var strengthAnalysis = StrengthAnalysis()
    var consistencyMetrics = ConsistencyMetrics()
}

struct WorkoutAnalytics {
    var totalWorkouts: Int = 0
    var totalDuration: TimeInterval = 0
    var averageDuration: TimeInterval = 0
    var completionRate: Double = 0
    var exerciseTypeDistribution: [ExerciseType: Int] = [:]
    var mostFrequentExercises: [ExerciseType] = []
}

struct ProgressMetrics {
    var durationImprovement: Double = 0
    var completionRateImprovement: Double = 0
    var currentStreak: Int = 0
    var longestStreak: Int = 0
}

struct PerformanceTrend {
    let date: Date
    let averageDuration: TimeInterval
    let completionRate: Double
    let workoutCount: Int
}

struct StrengthAnalysis {
    var muscleGroupDistribution: [MuscleGroup: Int] = [:]
    var strongestMuscleGroups: [MuscleGroup] = []
    var weakPoints: [MuscleGroup] = []
}

struct ConsistencyMetrics {
    var workoutFrequency: Double = 0 // Workouts per week
    var consistencyScore: Double = 0 // 0-1 scale
    var averageRestDays: Double = 0
}

struct ExercisePerformanceTrend {
    let date: Date
    let exerciseType: ExerciseType
    let averageReps: Int
    let averageSets: Int
    let volume: Int
}

struct StrengthProgression {
    var overallImprovement: Double = 0
    var timespan: TimeInterval = 0
}

struct ConsistencyAnalysis {
    var averageWeeklyWorkouts: Double = 0
    var consistencyVariance: Double = 0
}

struct AnalyticsInsight {
    let type: InsightType
    let title: String
    let description: String
    let priority: Priority
    let actionable: Bool
    
    enum InsightType {
        case achievement, improvement, suggestion, warning
    }
    
    enum Priority {
        case low, medium, high
    }
}

struct PersonalizedRecommendation {
    let title: String
    let description: String
    let category: Category
    let priority: Priority
    let estimatedImpact: Impact
    
    enum Category {
        case workoutAdjustment, frequency, recovery, technique
    }
    
    enum Priority {
        case low, medium, high
    }
    
    enum Impact {
        case low, medium, high
    }
}

struct AnalyticsReport: Codable {
    let timeframe: AnalyticsTimeframe
    let workoutAnalytics: WorkoutAnalytics
    let progressMetrics: ProgressMetrics
    let performanceTrends: [PerformanceTrend]
    let strengthAnalysis: StrengthAnalysis
    let consistencyMetrics: ConsistencyMetrics
    let insights: [AnalyticsInsight]
    let generatedDate: Date
    
    static func empty(timeframe: AnalyticsTimeframe) -> AnalyticsReport {
        return AnalyticsReport(
            timeframe: timeframe,
            workoutAnalytics: WorkoutAnalytics(),
            progressMetrics: ProgressMetrics(),
            performanceTrends: [],
            strengthAnalysis: StrengthAnalysis(),
            consistencyMetrics: ConsistencyMetrics(),
            insights: [],
            generatedDate: Date()
        )
    }
}

// MARK: - Logger Category Extension
extension Logger.Category {
    static let analytics = Logger.Category("analytics")
}
