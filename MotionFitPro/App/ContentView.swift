import SwiftUI
import SwiftData

struct ContentView: View {
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject private var appCoordinator: AppCoordinator
    @State private var isLoading = true
    
    var body: some View {
        Group {
            if isLoading {
                LaunchView()
                    .onAppear {
                        // Simulate a launch process
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                            withAnimation(.easeInOut(duration: 0.5)) {
                                isLoading = false
                            }
                        }
                    }
            } else {
                MainNavigationView()
            }
        }
        .animation(.easeInOut, value: isLoading)
        .onAppear {
            // Generate sample data on first launch if needed
            Task {
                await SampleDataGenerator.generateSampleData(modelContext: modelContext)
            }
        }
    }
}

struct LaunchView: View {
    var body: some View {
        ZStack {
            LinearGradient(
                gradient: Gradient(colors: [.blue, .purple]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 20) {
                Image(systemName: "figure.run")
                    .font(.system(size: 80))
                    .foregroundColor(.white)
                
                Text("MotionFitPro")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text("AI-Powered Motion Tracking")
                    .font(.headline)
                    .foregroundColor(.white.opacity(0.8))
                
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.2)
                    .padding(.top, 40)
            }
        }
    }
}

struct MainNavigationView: View {
    @EnvironmentObject private var appCoordinator: AppCoordinator
    
    var body: some View {
        TabView(selection: $appCoordinator.selectedTab) {
            HomeView()
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("Home")
                }
                .tag(AppCoordinator.Tab.home)
            
            WorkoutSelectionView()
                .tabItem {
                    Image(systemName: "figure.run")
                    Text("Workouts")
                }
                .tag(AppCoordinator.Tab.workouts)
            
            ProgressViewTab()
                .tabItem {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                    Text("Progress")
                }
                .tag(AppCoordinator.Tab.progress)
            
            ProfileView()
                .tabItem {
                    Image(systemName: "person.fill")
                    Text("Profile")
                }
                .tag(AppCoordinator.Tab.profile)
        }
        .tint(.blue)
    }
}

struct HomeView: View {
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("Welcome to MotionFitPro")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("AI-Powered Motion Tracking Workouts")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Button("Start Quick Workout") {
                        // Placeholder action
                        print("Starting workout...")
                    }
                    .frame(height: 50)
                    .frame(maxWidth: .infinity)
                    .background(.blue)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                    .padding(.horizontal)
                }
                .padding()
            }
            .navigationTitle("Home")
        }
    }
}

struct WorkoutSelectionView: View {
    @Query(sort: \WorkoutSession.startTime, order: .reverse) private var workoutSessions: [WorkoutSession]
    
    var body: some View {
        NavigationView {
            List(workoutSessions) { workout in
                VStack(alignment: .leading) {
                    Text(workout.name)
                        .font(.headline)
                    Text("Started: \(workout.startTime, formatter: itemFormatter)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .navigationTitle("Workouts")
        }
    }
}

// Renamed to avoid conflict with SwiftUI.ProgressView
struct ProgressViewTab: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("Progress Tracking")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                Text("Coming Soon")
                    .foregroundColor(.secondary)
            }
            .navigationTitle("Progress")
        }
    }
}

struct ProfileView: View {
    var body: some View {
        NavigationView {
            VStack {
                Text("User Profile")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                Text("Coming Soon")
                    .foregroundColor(.secondary)
            }
            .navigationTitle("Profile")
        }
    }
}

private let itemFormatter: DateFormatter = {
    let formatter = DateFormatter()
    formatter.dateStyle = .short
    formatter.timeStyle = .medium
    return formatter
}()

#Preview {
    // Setup in-memory container for preview
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: WorkoutSession.self, configurations: config)
    
    // Add sample data for the preview
    Task { @MainActor in
        SampleDataGenerator.generateSampleData(modelContext: container.mainContext)
    }
    
    return ContentView()
        .modelContainer(container)
        .environmentObject(AppCoordinator())
        .environmentObject(ARSessionManager.shared)
        .environmentObject(MLProcessingManager.shared)
        .environmentObject(AudioManager.shared)
}