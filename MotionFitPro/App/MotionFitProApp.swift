import SwiftUI
import SwiftData

@main
struct MotionFitProApp: App {
    let dataController = DataController.shared

    var body: some Scene {
        WindowGroup {
            ContentView()
                .modelContainer(dataController.container)
                .environmentObject(AppCoordinator())
                .environmentObject(ARSessionManager.shared)
                .environmentObject(MLProcessingManager.shared)
                .environmentObject(AudioManager.shared)
                .onAppear {
                    setupApp()
                }
        }
    }

    private func setupApp() {
        // Initialize core services
        Logger.shared.info("MotionFitPro app starting", category: .app)

        // Setup logging
        setupLogging()

        // Check permissions
        checkPermissions()

        Logger.shared.info("MotionFitPro app setup complete", category: .app)
    }

    private func setupLogging() {
        // Configure logging levels based on build configuration
        #if DEBUG
        Logger.shared.info("Debug logging enabled", category: .app)
        #else
        Logger.shared.info("Release logging enabled", category: .app)
        #endif
    }

    private func checkPermissions() {
        // Check camera permissions for AR
        Logger.shared.info("Checking camera permissions", category: .app)

        // Check microphone permissions for audio feedback
        Logger.shared.info("Checking microphone permissions", category: .app)

        // Note: Actual permission checking will be implemented in later phases
    }
}