import Foundation
import simd

/// Analyzes exercise form using biomechanical principles and pose data
class FormAnalyzer {
    
    // MARK: - Properties
    private let logger = Logger.shared
    private var analysisHistory: [FormAnalysis] = []
    private let maxHistorySize = 30
    
    // Form thresholds for different exercises
    private let formThresholds: [ExerciseType: FormThresholds] = [
        .squat: FormThresholds(
            jointAlignment: 0.8,
            rangeOfMotion: 0.75,
            stability: 0.7,
            timing: 0.8,
            symmetry: 0.85
        ),
        .pushUp: FormThresholds(
            jointAlignment: 0.85,
            rangeOfMotion: 0.8,
            stability: 0.75,
            timing: 0.7,
            symmetry: 0.8
        ),
        .plank: FormThresholds(
            jointAlignment: 0.9,
            rangeOfMotion: 0.6, // Less relevant for isometric
            stability: 0.85,
            timing: 0.9,
            symmetry: 0.85
        )
    ]
    
    // MARK: - Public Interface
    
    /// Analyze form for a specific exercise
    func analyzeForm(_ poseData: BodyPoseData, for exerciseType: ExerciseType) async -> FormAnalysis {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        var scores: [FormCriteria: Float] = [:]
        var criticalIssues: [FormIssue] = []
        var recommendations: [String] = []
        
        // Analyze each form criteria
        scores[.jointAlignment] = analyzeJointAlignment(poseData, for: exerciseType)
        scores[.rangeOfMotion] = analyzeRangeOfMotion(poseData, for: exerciseType)
        scores[.stability] = analyzeStability(poseData, for: exerciseType)
        scores[.timing] = analyzeTiming(poseData, for: exerciseType)
        scores[.symmetry] = analyzeSymmetry(poseData, for: exerciseType)
        
        // Calculate overall score
        let overallScore = calculateOverallScore(scores, for: exerciseType)
        
        // Identify critical issues
        criticalIssues = identifyCriticalIssues(scores, poseData: poseData, exerciseType: exerciseType)
        
        // Generate recommendations
        recommendations = generateRecommendations(scores, exerciseType: exerciseType)
        
        let analysis = FormAnalysis(
            scores: scores,
            overallScore: overallScore,
            criticalIssues: criticalIssues,
            recommendations: recommendations
        )
        
        // Add to history
        addToHistory(analysis)
        
        let processingTime = CFAbsoluteTimeGetCurrent() - startTime
        logger.debug("Form analysis completed in \(processingTime * 1000)ms", category: .formAnalysis)
        
        return analysis
    }
    
    // MARK: - Joint Alignment Analysis
    
    private func analyzeJointAlignment(_ poseData: BodyPoseData, for exerciseType: ExerciseType) -> Float {
        switch exerciseType {
        case .squat:
            return analyzeSquatAlignment(poseData)
        case .pushUp:
            return analyzePushUpAlignment(poseData)
        case .plank:
            return analyzePlankAlignment(poseData)
        default:
            return 0.5 // Default neutral score
        }
    }
    
    private func analyzeSquatAlignment(_ poseData: BodyPoseData) -> Float {
        guard let leftKnee = poseData.joints[.leftLowerLeg],
              let rightKnee = poseData.joints[.rightLowerLeg],
              let leftAnkle = poseData.joints[.leftFoot],
              let rightAnkle = poseData.joints[.rightFoot],
              let leftHip = poseData.joints[.leftUpperLeg],
              let rightHip = poseData.joints[.rightUpperLeg] else {
            return 0.0
        }
        
        var alignmentScore: Float = 1.0
        
        // Check knee tracking over toes
        let leftKneeAnkleAlignment = calculateAlignment(leftKnee.position, leftAnkle.position)
        let rightKneeAnkleAlignment = calculateAlignment(rightKnee.position, rightAnkle.position)
        
        // Penalize if knees cave inward or bow outward
        if abs(leftKneeAnkleAlignment) > 0.1 { alignmentScore -= 0.3 }
        if abs(rightKneeAnkleAlignment) > 0.1 { alignmentScore -= 0.3 }
        
        // Check hip-knee-ankle alignment
        let leftHipKneeAnkleAngle = calculateAngle(leftHip.position, leftKnee.position, leftAnkle.position)
        let rightHipKneeAnkleAngle = calculateAngle(rightHip.position, rightKnee.position, rightAnkle.position)
        
        // Ideal angle should be close to 180 degrees (straight line)
        let leftAngleDeviation = abs(leftHipKneeAnkleAngle - Float.pi)
        let rightAngleDeviation = abs(rightHipKneeAnkleAngle - Float.pi)
        
        if leftAngleDeviation > 0.3 { alignmentScore -= 0.2 }
        if rightAngleDeviation > 0.3 { alignmentScore -= 0.2 }
        
        return max(0.0, alignmentScore)
    }
    
    private func analyzePushUpAlignment(_ poseData: BodyPoseData) -> Float {
        guard let head = poseData.joints[.head],
              let shoulders = poseData.joints[.spine6],
              let hips = poseData.joints[.root],
              let ankles = poseData.joints[.leftFoot] else {
            return 0.0
        }
        
        // Check for straight line from head to ankles
        let bodyLineAngle = calculateAngle(head.position, shoulders.position, hips.position)
        let hipAnkleAngle = calculateAngle(shoulders.position, hips.position, ankles.position)
        
        var alignmentScore: Float = 1.0
        
        // Penalize sagging hips or pike position
        let headShoulderHipDeviation = abs(bodyLineAngle - Float.pi)
        let shoulderHipAnkleDeviation = abs(hipAnkleAngle - Float.pi)
        
        if headShoulderHipDeviation > 0.2 { alignmentScore -= 0.4 }
        if shoulderHipAnkleDeviation > 0.2 { alignmentScore -= 0.4 }
        
        return max(0.0, alignmentScore)
    }
    
    private func analyzePlankAlignment(_ poseData: BodyPoseData) -> Float {
        // Similar to push-up but more strict on maintaining position
        return analyzePushUpAlignment(poseData) * 1.1 // Slightly more forgiving
    }
    
    // MARK: - Range of Motion Analysis
    
    private func analyzeRangeOfMotion(_ poseData: BodyPoseData, for exerciseType: ExerciseType) -> Float {
        switch exerciseType {
        case .squat:
            return analyzeSquatDepth(poseData)
        case .pushUp:
            return analyzePushUpDepth(poseData)
        case .plank:
            return 1.0 // Isometric exercise - full score for maintaining position
        default:
            return 0.5
        }
    }
    
    private func analyzeSquatDepth(_ poseData: BodyPoseData) -> Float {
        guard let leftHip = poseData.joints[.leftUpperLeg],
              let rightHip = poseData.joints[.rightUpperLeg],
              let leftKnee = poseData.joints[.leftLowerLeg],
              let rightKnee = poseData.joints[.rightLowerLeg] else {
            return 0.0
        }
        
        // Calculate hip and knee heights
        let hipHeight = (leftHip.position.y + rightHip.position.y) / 2
        let kneeHeight = (leftKnee.position.y + rightKnee.position.y) / 2
        
        // Ideal squat: hips should be at or below knee level
        let depthRatio = hipHeight / kneeHeight
        
        if depthRatio <= 1.0 {
            return 1.0 // Perfect depth
        } else if depthRatio <= 1.2 {
            return 0.8 // Good depth
        } else if depthRatio <= 1.4 {
            return 0.6 // Acceptable depth
        } else {
            return 0.3 // Shallow squat
        }
    }
    
    private func analyzePushUpDepth(_ poseData: BodyPoseData) -> Float {
        guard let chest = poseData.joints[.spine4],
              let hands = poseData.joints[.leftHand] else {
            return 0.0
        }
        
        // Calculate distance from chest to hand level
        let chestToHandDistance = abs(chest.position.y - hands.position.y)
        
        // Ideal push-up: chest should be close to hand level
        if chestToHandDistance <= 0.1 {
            return 1.0 // Excellent depth
        } else if chestToHandDistance <= 0.2 {
            return 0.8 // Good depth
        } else if chestToHandDistance <= 0.3 {
            return 0.6 // Acceptable depth
        } else {
            return 0.3 // Shallow push-up
        }
    }
    
    // MARK: - Stability Analysis
    
    private func analyzeStability(_ poseData: BodyPoseData, for exerciseType: ExerciseType) -> Float {
        // Use pose history to analyze movement stability
        guard analysisHistory.count >= 5 else { return 0.5 }
        
        let recentAnalyses = Array(analysisHistory.suffix(5))
        var stabilityScore: Float = 1.0
        
        // Calculate variance in joint positions over recent frames
        let keyJoints: [JointName] = getKeyJointsForExercise(exerciseType)
        
        for jointName in keyJoints {
            guard let currentJoint = poseData.joints[jointName] else { continue }
            
            let positions = recentAnalyses.compactMap { analysis in
                // This would need to be implemented to track joint positions over time
                return currentJoint.position
            }
            
            if positions.count >= 3 {
                let variance = calculatePositionVariance(positions)
                if variance > 0.05 { // High variance indicates instability
                    stabilityScore -= 0.2
                }
            }
        }
        
        return max(0.0, stabilityScore)
    }
    
    // MARK: - Timing Analysis
    
    private func analyzeTiming(_ poseData: BodyPoseData, for exerciseType: ExerciseType) -> Float {
        // This would require tracking movement phases over time
        // For now, return a baseline score
        return 0.8
    }
    
    // MARK: - Symmetry Analysis
    
    private func analyzeSymmetry(_ poseData: BodyPoseData, for exerciseType: ExerciseType) -> Float {
        var symmetryScore: Float = 1.0
        
        // Compare left and right side joint positions
        let symmetryPairs: [(JointName, JointName)] = [
            (.leftShoulder, .rightShoulder),
            (.leftUpperArm, .rightUpperArm),
            (.leftLowerArm, .rightLowerArm),
            (.leftUpperLeg, .rightUpperLeg),
            (.leftLowerLeg, .rightLowerLeg),
            (.leftFoot, .rightFoot)
        ]
        
        for (leftJoint, rightJoint) in symmetryPairs {
            guard let leftPos = poseData.joints[leftJoint],
                  let rightPos = poseData.joints[rightJoint] else { continue }
            
            // Calculate symmetry based on height difference
            let heightDifference = abs(leftPos.position.y - rightPos.position.y)
            if heightDifference > 0.1 { // 10cm difference threshold
                symmetryScore -= 0.15
            }
        }
        
        return max(0.0, symmetryScore)
    }
    
    // MARK: - Helper Methods
    
    private func calculateOverallScore(_ scores: [FormCriteria: Float], for exerciseType: ExerciseType) -> Float {
        guard let thresholds = formThresholds[exerciseType] else { return 0.5 }
        
        let weights: [FormCriteria: Float] = [
            .jointAlignment: 0.3,
            .rangeOfMotion: 0.25,
            .stability: 0.2,
            .timing: 0.15,
            .symmetry: 0.1
        ]
        
        var weightedSum: Float = 0.0
        var totalWeight: Float = 0.0
        
        for (criteria, weight) in weights {
            if let score = scores[criteria] {
                weightedSum += score * weight
                totalWeight += weight
            }
        }
        
        return totalWeight > 0 ? weightedSum / totalWeight : 0.0
    }
    
    private func identifyCriticalIssues(_ scores: [FormCriteria: Float], poseData: BodyPoseData, exerciseType: ExerciseType) -> [FormIssue] {
        var issues: [FormIssue] = []
        
        for (criteria, score) in scores {
            if score < 0.5 { // Critical threshold
                let issue = FormIssue(
                    criteria: criteria,
                    severity: .high,
                    description: "Poor \(criteria.displayName.lowercased()) detected",
                    jointInvolved: getRelevantJoint(for: criteria, exerciseType: exerciseType)
                )
                issues.append(issue)
            }
        }
        
        return issues
    }
    
    private func generateRecommendations(_ scores: [FormCriteria: Float], exerciseType: ExerciseType) -> [String] {
        var recommendations: [String] = []
        
        for (criteria, score) in scores {
            if score < 0.7 {
                let recommendation = getRecommendation(for: criteria, exerciseType: exerciseType)
                recommendations.append(recommendation)
            }
        }
        
        return recommendations
    }
    
    private func getKeyJointsForExercise(_ exerciseType: ExerciseType) -> [JointName] {
        switch exerciseType {
        case .squat:
            return [.root, .leftUpperLeg, .rightUpperLeg, .leftLowerLeg, .rightLowerLeg]
        case .pushUp:
            return [.leftShoulder, .rightShoulder, .root, .leftHand, .rightHand]
        case .plank:
            return [.head, .spine6, .root, .leftFoot, .rightFoot]
        default:
            return [.root, .spine3, .leftShoulder, .rightShoulder]
        }
    }
    
    private func getRelevantJoint(for criteria: FormCriteria, exerciseType: ExerciseType) -> JointName? {
        switch (criteria, exerciseType) {
        case (.jointAlignment, .squat): return .leftLowerLeg
        case (.jointAlignment, .pushUp): return .spine6
        case (.rangeOfMotion, .squat): return .root
        case (.rangeOfMotion, .pushUp): return .spine4
        default: return nil
        }
    }
    
    private func getRecommendation(for criteria: FormCriteria, exerciseType: ExerciseType) -> String {
        switch (criteria, exerciseType) {
        case (.jointAlignment, .squat): return "Keep knees aligned with toes"
        case (.jointAlignment, .pushUp): return "Maintain straight body line"
        case (.rangeOfMotion, .squat): return "Squat deeper - hips below knees"
        case (.rangeOfMotion, .pushUp): return "Lower chest closer to ground"
        case (.stability, _): return "Slow down and focus on control"
        case (.symmetry, _): return "Keep both sides moving equally"
        default: return "Focus on proper form"
        }
    }
    
    private func calculateAlignment(_ pos1: simd_float3, _ pos2: simd_float3) -> Float {
        return pos1.x - pos2.x // Horizontal alignment
    }
    
    private func calculateAngle(_ pos1: simd_float3, _ pos2: simd_float3, _ pos3: simd_float3) -> Float {
        let vector1 = pos1 - pos2
        let vector2 = pos3 - pos2
        
        let dot = simd_dot(vector1, vector2)
        let magnitude1 = simd_length(vector1)
        let magnitude2 = simd_length(vector2)
        
        guard magnitude1 > 0 && magnitude2 > 0 else { return 0 }
        
        let cosAngle = dot / (magnitude1 * magnitude2)
        return acos(max(-1, min(1, cosAngle)))
    }
    
    private func calculatePositionVariance(_ positions: [simd_float3]) -> Float {
        guard positions.count > 1 else { return 0 }
        
        let mean = positions.reduce(simd_float3(0, 0, 0)) { $0 + $1 } / Float(positions.count)
        let variance = positions.reduce(Float(0)) { result, pos in
            let diff = pos - mean
            return result + simd_length_squared(diff)
        } / Float(positions.count)
        
        return sqrt(variance)
    }
    
    private func addToHistory(_ analysis: FormAnalysis) {
        analysisHistory.append(analysis)
        if analysisHistory.count > maxHistorySize {
            analysisHistory.removeFirst()
        }
    }
}

// MARK: - Supporting Types

struct FormThresholds {
    let jointAlignment: Float
    let rangeOfMotion: Float
    let stability: Float
    let timing: Float
    let symmetry: Float
}

// MARK: - Logger Category Extension
extension Logger.Category {
    static let formAnalysis = Logger.Category("formAnalysis")
}
