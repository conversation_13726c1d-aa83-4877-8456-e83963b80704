//
//  ExerciseType.swift
//  MotionFitPro
//
//  Created by Cascade on 2025-07-14.
//

import Foundation
import ARKit

/// An enumeration of all exercises that the app can recognize and track.
enum ExerciseType: String, CaseIterable, Codable {
    case squat
    case pushUp
    case lunge
    case jumpingJack
    case plank
    case unknown

    /// A user-friendly display name for the exercise.
    var displayName: String {
        switch self {
        case .squat: return "Squat"
        case .pushUp: return "Push-Up"
        case .lunge: return "Lunge"
        case .jumpingJack: return "Jumping Jack"
        case .plank: return "Plank"
        case .unknown: return "Unknown Exercise"
        }
    }

    /// The primary muscle groups targeted by the exercise.
    var primaryMuscles: [String] {
        switch self {
        case .squat: return ["Quadriceps", "Glutes", "Hamstrings"]
        case .pushUp: return ["Pectorals", "Deltoids", "Triceps"]
        case .lunge: return ["Quadriceps", "Glutes", "Hamstrings"]
        case .jumpingJack: return ["Calves", "Shoulders", "Adductors"]
        case .plank: return ["Abdominals", "Obliques", "<PERSON>re<PERSON> Spinae"]
        case .unknown: return []
        }
    }

    /// The key joints that are critical for tracking the form of this exercise.
    var keyJoints: [ARSkeleton.JointName] {
        switch self {
        case .squat: return [.leftKnee, .rightKnee, .leftHip, .rightHip]
        case .pushUp: return [.leftElbow, .rightElbow, .leftShoulder, .rightShoulder]
        case .lunge: return [.leftKnee, .rightKnee, .leftHip, .rightHip]
        case .jumpingJack: return [.leftShoulder, .rightShoulder, .leftHip, .rightHip]
        case .plank: return [.leftHip, .rightHip, .spine]
        case .unknown: return []
        }
    }
}
